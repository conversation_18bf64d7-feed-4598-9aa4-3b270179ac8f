from machine import Pin
from utils.time_util import sleep

pins = [
    <PERSON>n(5, Pin.OUT),
    <PERSON><PERSON>(18, Pin.OUT),
    <PERSON><PERSON>(19, Pin.OUT),
    <PERSON><PERSON>(21, Pin.OUT)
]

turn_per_angle = 512 / 360


def turn(status_list):
    for index, status in enumerate(status_list):
        pins[index].value(status)


def front_turn(speed):
    turn([1, 1, 0, 0])
    sleep(speed)
    turn([0, 1, 1, 0])
    sleep(speed)
    turn([0, 0, 1, 1])
    sleep(speed)
    turn([1, 0, 0, 1])
    sleep(speed)


def back_turn(speed):
    turn([1, 1, 0, 0])
    sleep(speed)
    turn([1, 0, 0, 1])
    sleep(speed)
    turn([0, 0, 1, 1])
    sleep(speed)
    turn([0, 1, 1, 0])
    sleep(speed)


def stop_turn():
    turn([0, 0, 0, 0])


def turn_angle(angle, speed=5):
    turns = int(turn_per_angle * abs(angle))
    if angle >= 0:
        for turn in range(turns):
            front_turn(speed)
    elif angle < 0:
        for turn in range(turns):
            back_turn(speed)
    stop_turn()
    