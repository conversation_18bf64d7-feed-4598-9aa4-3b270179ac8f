#include "include/common.h"
#include "include/utils/serial.h"
#include "include/utils/utils.h"
#include "include/utils/wifi_s.h"
#include "include/utils/mqtt_s.h"
#include "include/databases/keypad_database.h"
#include "include/checks/motor_check.h"
#include "include/checks/reset_check.h"
#include "include/controls/sleep_control.h"
#include "include/controls/keypad_control.h"
#include "include/business/sleep_business.h"
#include "include/business/bluetooth_business.h"
#include "include/business/mqtt_callback_business.h"
#include "include/business/keypad_business.h"
#include "include/business/fpm383_business.h"

// 当前循环次数
unsigned long current_loop = 0;

// MQTT 信息回调函数
void mqtt_callback(char *topic, uint8_t *message, unsigned int length)
{
  size_t size = get_json_size(10, 1, length);
  DynamicJsonDocument doc(size);
  DeserializationError error = deserializeJson(doc, message, length);
  if (error)
  {
    print_log("mqtt", "callback", "deserialization error!!");
  }
  else
  {
    char mqtt_topic_self[50];
    get_mqtt_topic_self(mqtt_topic_self);
    if (strcmp(topic, MQTT_TOPIC_PUBLIC) == 0)
    {
      mqtt_callback_public();
    }
    else if (strcmp(topic, mqtt_topic_self) == 0)
    {
      mqtt_callback_self(doc);
    }
  }
}

void setup()
{
  before_setup();
  // pinMode(PIN_POWER, OUTPUT);
  pinMode(PIN_LED_BLUETOOTH, OUTPUT);
  pinMode(PIN_LED_LOW_BATTERY, OUTPUT);
  pinMode(PIN_MOTOR_0, OUTPUT);
  pinMode(PIN_MOTOR_1, OUTPUT);
  check_motor_exception();
  serial_init();
  // keypad_init();
  // keypad_set_idle_time(0x00, 0x01);
  // 仅在电压稳定时初始化蓝牙和 WiFi
  esp_reset_reason_t reset_reason = esp_reset_reason();
  if (reset_reason != ESP_RST_BROWNOUT)
  {
    bluetooth_init(new BluetoothServerCallbacks(), new BluetoothCharacteristicCallbacks());
    wifi_init();
  }
  after_setup();
}

void loop()
{
  loop_process();
  before_loop();
  if (bluetooth_is_connected() && get_bluetooth_waiting_status())
  {
    std::string data;
    get_bluetooth_complete_data(data);
    bluetooth_message_analysis(data);
  }
  else
  {
    // fpm383_process(keypad_process);
    try_connect_mqtt(mqtt_callback);
    if (get_time_diff(get_last_sleep_report_time()) >= MQTT_START_PROCESS_TIME)
    {
      mqtt_receive();
      mqtt_report_states();
      mqtt_report_operation();
    }
  }
  after_loop();
  try_sleep(before_sleep);
  delay(10);
}

void loop_process()
{
  current_loop += 1;
  switch (current_loop)
  {
  case 1:
    check_reset_exception();
    break;
  case 2:
    // fpm383_set_led(0x03, 0x04, 100, 0, 60);
    break;
  case 3:
  {
    float battery_percent = get_battery_percent(PIN_BATTERY, BATTERY_FULL_VALUE, BATTERY_EMPTY_VALUE);
    print_log("setup", "battery", "percent: " + String(battery_percent));
    if (battery_percent < BATTERY_LOW_THRESHOLD)
    {
      digitalWrite(PIN_LED_LOW_BATTERY, HIGH);
    }
  }
  break;
  default:
    break;
  }
}

// before setup
void before_setup()
{
}

// after setup
void after_setup()
{
}

// before loop
void before_loop()
{
}

// after loop
void after_loop()
{
}
