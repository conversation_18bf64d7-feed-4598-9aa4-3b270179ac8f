import os
import sys


def exist_directory(path):
    try:
        os.listdir(path)
        return True
    except Exception:
        return False


def exist_file(path):
    try:
        test_file = open(path, "r")
        test_file.close()
        return True
    except Exception:
        return False


def create_directory(path):
    try:
        if not exist_directory(path):
            os.mkdir(path)
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def create_file(path):
    try:
        if not exist_file(path):
            new_file = open(path, "w")
            new_file.close()
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def remove_directory(path):
    try:
        if exist_directory(path):
            os.rmdir(path)
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def remove_file(path):
    try:
        if exist_file(path):
            os.remove(path)
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def read_file(path):
    try:
        if exist_file(path):
            f = open(path, "r")
            content = f.read()
            f.close()
            return content
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def write_file(path, content):
    try:
        if exist_file(path):
            f = open(path, "w")
            f.write(content)
            f.close()
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False


def append_file(path, content):
    try:
        if exist_file(path):
            f = open(path, "a")
            f.write(content)
            f.close()
            return True
        return False
    except Exception as err:
        sys.print_exception(err)
        return False
