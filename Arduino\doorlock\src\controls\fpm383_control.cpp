#include "../../include/controls/fpm383_control.h"

/**
 * 指纹设置 LED
 *
 * 以下是常用 LED 配置，具体配置规则请查看 get_fpm383_command_led 函数说明或 FPM383 用户手册
 * 关闭 LED 灯光：mode = 0x00, color = 0x00
 * 蓝色渐变灯光：mode = 0x03, color = 0x04, p1 = 100, p2 = 0, p3 = 60
 * 绿色闪烁灯光：mode = 0x04, color = 0x01, p1 = 10, p2 = 10, p3 = 5
 * 红色闪烁灯光：mode = 0x04, color = 0x02, p1 = 10, p2 = 10, p3 = 5
 */
FPM383Status fpm383_set_led(char mode, char color, char p1, char p2, char p3)
{
  char command[23];
  get_fpm383_command_led(command, mode, color, p1, p2, p3);

  const int pack_size = 22;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(command, sizeof(command), 0);
  int size = fpm383_read(result, 100);
  if (size == pack_size)
  {
    char header[8];
    memcpy(header, result, 8 * sizeof(char));
    if (compare_array(FPM383_HEADER_V2, header, 8))
    {
      return FPM383Status::SUCCESS;
    }
  }
  else
  {
    print_log("fpm383", "set_led", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "set_led", "set led failed!!");
  return FPM383Status::FAILURE;
}

// 获取指纹触摸状态
FPM383Status fpm383_is_press(void (*process_on_delay)() = nullptr)
{
  const int pack_size = 23;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_IS_PRESS, sizeof(FPM383_IS_PRESS), 0);
  if (process_on_delay)
  {
    process_on_delay();
  }
  else
  {
    delay(100);
  }
  int size = fpm383_read(result, 100);
  if (size == pack_size)
  {
    char header[8];
    memcpy(header, result, 8 * sizeof(char));
    if (compare_array(FPM383_HEADER_V2, header, 8))
    {
      char status[1];
      char pressed_status[] = {0x01};
      memcpy(status, result + 21, 1 * sizeof(char));
      if (compare_array(pressed_status, status, 1))
      {
        return FPM383Status::SUCCESS;
      }
    }
  }
  else
  {
    print_log("fpm383", "is_press", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  return FPM383Status::FAILURE;
}

// 指纹验证
FPM383Status fpm383_verify(int &id_number)
{
  const int pack_size = 17;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_VALIDATE, sizeof(FPM383_VALIDATE), 500);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[2];
        char valid_status[] = {0x00, 0x05};
        memcpy(status, result + t * pack_size + 9, 2 * sizeof(char));
        if (compare_array(valid_status, status, 2))
        {
          char id_code[2];
          memcpy(id_code, result + t * pack_size + 11, 2 * sizeof(char));
          int number = id_code[0] << 8 | id_code[1];
          id_number = FPM383Database::getInstance().getUidByNumber(number);
          if (id_number > 0)
          {
            return FPM383Status::SUCCESS;
          }
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "verify", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  return FPM383Status::FAILURE;
}

// 指纹注册
FPM383Status fpm383_register(int uid, void (&callback)(int), int try_count)
{
  int number = FPM383Database::getInstance().getNext();
  if (number <= 0)
  {
    print_log("fpm383", "register", "no more number!!");
    return FPM383Status::NO_MORE_NUMBER;
  }
  char command[17];
  get_fpm383_command_register(command, number);

  const int pack_size = 14;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(command, sizeof(command), 500);
  for (int count = 0; count < try_count; count++)
  {
    delay(500);
    int size = fpm383_read(result, 100);
    if (size % pack_size == 0)
    {
      int pack_num = size / pack_size;
      for (int t = 0; t < pack_num; t++)
      {
        char header[6];
        memcpy(header, result + t * pack_size, 6 * sizeof(char));
        if (compare_array(FPM383_HEADER_V1, header, 6))
        {
          char status[1];
          char valid_status[] = {0x00};
          memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
          if (compare_array(valid_status, status, 1))
          {
            char params[2];
            char valid_params[] = {0x06, 0xF2};
            memcpy(params, result + t * pack_size + 10, 2 * sizeof(char));
            callback(params[1]);
            if (compare_array(valid_params, params, 2))
            {
              bool is_added = FPM383Database::getInstance().add(number, uid);
              if (is_added)
              {
                FPM383Database::getInstance().store();
                print_log("fpm383", "register", "register successed!!");
                return FPM383Status::SUCCESS;
              }
            }
          }
        }
      }
    }
    else
    {
      print_log("fpm383", "register", "received pack error!!");
      fpm383_write(FPM383_CANCEL, sizeof(FPM383_CANCEL), 0);
      return FPM383Status::ERROR_PACK;
    }
  }
  print_log("fpm383", "register", "register failed!!");
  fpm383_write(FPM383_CANCEL, sizeof(FPM383_CANCEL), 0);
  return FPM383Status::FAILURE;
}

// 指纹取消
FPM383Status fpm383_cancel()
{
  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_CANCEL, sizeof(FPM383_CANCEL), 100);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          print_log("fpm383", "cancel", "cancel successed!!");
          return FPM383Status::SUCCESS;
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "cancel", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "cancel", "cancel failed!!");
  return FPM383Status::FAILURE;
}

// 指纹储存模板
FPM383Status fpm383_store(int uid)
{
  int number = FPM383Database::getInstance().getNext();
  if (number <= 0)
  {
    print_log("fpm383", "store", "no more number!!");
    return FPM383Status::NO_MORE_NUMBER;
  }
  char command[15];
  get_fpm383_command_store(command, number);

  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(command, sizeof(command), 500);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          bool is_added = FPM383Database::getInstance().add(number, uid);
          if (is_added)
          {
            FPM383Database::getInstance().store();
            print_log("fpm383", "store", "store successed!!");
            return FPM383Status::SUCCESS;
          }
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "store", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "store", "store failed!!");
  return FPM383Status::FAILURE;
}

// 指纹下载准备
FPM383Status fpm383_pre_download()
{
  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_DOWNLOAD, sizeof(FPM383_DOWNLOAD), 200);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          print_log("fpm383", "pre_download", "download is ready!!");
          return FPM383Status::SUCCESS;
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "pre_download", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "pre_download", "pre download failed!!");
  return FPM383Status::FAILURE;
}

// 按键下载流程
void fpm383_download_process(String *fpm383_packs)
{
  for (int t = 0; t < FPM383_PACK_NUM; t++)
  {
    String pack = fpm383_packs[t];
    size_t length = pack.length() / 2;
    char data[length];
    hex_string_to_char_array(pack, data, length);
    fpm383_write(data, sizeof(data), 50);
  }
}

// 指纹删除
FPM383Status fpm383_remove(int uid)
{
  int number = FPM383Database::getInstance().getNumberByUid(uid);
  if (number <= 0)
  {
    print_log("fpm383", "remove", "no number found!!");
    return FPM383Status::FIND_NO_NUMBER;
  }
  char command[16];
  get_fpm383_command_remove(command, number);

  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(command, sizeof(command), 500);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          int number = FPM383Database::getInstance().getNumberByUid(uid);
          bool is_removed = FPM383Database::getInstance().remove(number);
          if (is_removed)
          {
            FPM383Database::getInstance().store();
            print_log("fpm383", "remove", "remove successed!!");
            return FPM383Status::SUCCESS;
          }
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "remove", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "remove", "remove failed!!");
  return FPM383Status::FAILURE;
}

// 指纹清空
FPM383Status fpm383_clear()
{
  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_CLEAR, sizeof(FPM383_CLEAR), 5000);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          print_log("fpm383", "clear", "clear successed!!");
          return FPM383Status::SUCCESS;
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "clear", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "clear", "clear failed!!");
  return FPM383Status::FAILURE;
}

// 指纹睡眠
FPM383Status fpm383_sleep()
{
  const int pack_size = 12;
  char result[100];
  fpm383_read(result, 100);
  fpm383_write(FPM383_SLEEP, sizeof(FPM383_SLEEP), 200);
  int size = fpm383_read(result, 100);
  if (size % pack_size == 0)
  {
    int pack_num = size / pack_size;
    for (int t = 0; t < pack_num; t++)
    {
      char header[6];
      memcpy(header, result + t * pack_size, 6 * sizeof(char));
      if (compare_array(FPM383_HEADER_V1, header, 6))
      {
        char status[1];
        char valid_status[] = {0x00};
        memcpy(status, result + t * pack_size + 9, 1 * sizeof(char));
        if (compare_array(valid_status, status, 1))
        {
          print_log("fpm383", "sleep", "sleep successed!!");
          return FPM383Status::SUCCESS;
        }
      }
    }
  }
  else
  {
    print_log("fpm383", "sleep", "received pack error!!");
    return FPM383Status::ERROR_PACK;
  }
  print_log("fpm383", "sleep", "sleep failed!!");
  return FPM383Status::FAILURE;
}

// 指纹验证流程
FPM383VerifyResult fpm383_verify_process(void (&process_on_delay)())
{
  FPM383VerifyResult result;
  FPM383Status is_press = fpm383_is_press(process_on_delay);
  if (is_press == FPM383Status::SUCCESS)
  {
    result.isPress = true;
    int id_number = 0;
    FPM383Status is_validate = fpm383_verify(id_number);
    if (is_validate == FPM383Status::SUCCESS)
    {
      result.isPass = true;
      result.userId = id_number;
    }
  }
  return result;
}
