#ifndef HW_S_FPM383_CONTROL_H
#define HW_S_FPM383_CONTROL_H

#include "../common.h"
#include "../utils/utils.h"
#include "../utils/serial.h"
#include "../databases/fpm383_database.h"

// 指纹相关状态枚举
enum FPM383Status
{
  SUCCESS,
  FAILURE,
  ERROR_PACK,
  NO_MORE_NUMBER,
  FIND_NO_NUMBER
};

// 指纹验证结果结构体
struct FPM383VerifyResult
{
  bool isPress;
  bool isPass;
  int userId;

  FPM383VerifyResult() : isPress(false), isPass(false), userId(0){};
};

/**
 * 指纹设置 LED
 *
 * 以下是常用 LED 配置，具体配置规则请查看 get_fpm383_command_led 函数说明或 FPM383 用户手册
 * 关闭 LED 灯光：mode = 0x00, color = 0x00
 * 蓝色渐变灯光：mode = 0x03, color = 0x04, p1 = 100, p2 = 0, p3 = 60
 * 绿色闪烁灯光：mode = 0x04, color = 0x01, p1 = 10, p2 = 10, p3 = 5
 * 红色闪烁灯光：mode = 0x04, color = 0x02, p1 = 10, p2 = 10, p3 = 5
 */
FPM383Status fpm383_set_led(char mode, char color, char p1 = 0, char p2 = 0, char p3 = 0);

// 指纹注册
FPM383Status fpm383_register(int uid, void (&callback)(int), int try_count = 40);

// 指纹取消
FPM383Status fpm383_cancel();

// 指纹储存模板
FPM383Status fpm383_store(int uid);

// 指纹下载准备
FPM383Status fpm383_pre_download();

// 按键下载流程
void fpm383_download_process(String *fpm383_packs);

// 指纹删除
FPM383Status fpm383_remove(int uid);

// 指纹清空
FPM383Status fpm383_clear();

// 指纹睡眠
FPM383Status fpm383_sleep();

// 指纹验证流程
FPM383VerifyResult fpm383_verify_process(void (&process_on_delay)());

#endif
