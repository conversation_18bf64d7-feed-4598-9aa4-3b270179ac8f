import json
from utils.file_util import *


class ConfigHelper():
    __instance = None

    def __init__(self):
        self.__directory_name = "/config"

    @classmethod
    def get_instance(cls, *args, **kwargs):
        if not cls.__instance:
            cls.__instance = cls(*args, **kwargs)
        return cls.__instance

    def __get_config_file_name(self, name):
        return f"{self.__directory_name}/{name}.json"

    def create_config(self, configs):
        try:
            if not exist_directory(self.__directory_name):
                create_directory(self.__directory_name)
            for name in configs:
                if name and isinstance(name, str):
                    file_name = self.__get_config_file_name(name)
                    if not exist_file(file_name):
                        return create_file(file_name) and write_file(file_name, json.dumps({}))
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def get_configs(self, name, key):
        file_name = self.__get_config_file_name(name)
        try:
            content = read_file(file_name)
            if isinstance(content, str):
                content = read_file(file_name)
                config_content = json.loads(content)
                if config_content and isinstance(config_content, dict):
                    return config_content.get(key)
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def set_configs(self, name, key, value):
        file_name = self.__get_config_file_name(name)
        try:
            content = read_file(file_name)
            if isinstance(content, str):
                config_content = json.loads(content)
                if not isinstance(config_content, dict):
                    config_content = {}
                config_content[key] = value
                return write_file(file_name, json.dumps(config_content))
            return False
        except Exception as err:
            sys.print_exception(err)
            return False
