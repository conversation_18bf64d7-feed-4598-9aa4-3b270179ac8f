#include "../../include/utils/bluetooth_s.h"

// UUID
const std::string SERVICE_UUID = "156A8071-B28B-48DC-A981-F7242A1DCDAC";
const std::string WRITE_UUID = "156A8072-B28B-48DC-A981-F7242A1DCDAC";
const std::string READ_NOTIFY_UUID = "156A8073-B28B-48DC-A981-F7242A1DCDAC";

// Bluetooth
BLEServer *pServer = nullptr;
BLECharacteristic *pReadNotifyCharacteristic = nullptr;
BLECharacteristic *pWriteNoResponseCharacteristic = nullptr;

// 初始化蓝牙
void bluetooth_init(BLEServerCallbacks *serverCallbacks, BLECharacteristicCallbacks *characteristicCallbacks)
{
  char bluetooth_name[50];
  get_bluetooth_name(bluetooth_name);
  BLEDevice::init(bluetooth_name);

  pServer = BLEDevice::createServer();
  pServer->setCallbacks(serverCallbacks);
  BLEService *pService = pServer->createService(BLEUUID(SERVICE_UUID));
  pReadNotifyCharacteristic = pService->createCharacteristic(
      BLEUUID(READ_NOTIFY_UUID),
      BLECharacteristic::PROPERTY_READ | BLECharacteristic::PROPERTY_NOTIFY);
  pReadNotifyCharacteristic->addDescriptor(new BLE2902());
  pWriteNoResponseCharacteristic = pService->createCharacteristic(
      BLEUUID(WRITE_UUID),
      BLECharacteristic::PROPERTY_WRITE | BLECharacteristic::PROPERTY_WRITE_NR);
  pWriteNoResponseCharacteristic->setCallbacks(characteristicCallbacks);

  pService->start();
  BLEAdvertising *pAdvertising = BLEDevice::getAdvertising();
  pAdvertising->addServiceUUID(SERVICE_UUID);
  pAdvertising->start();
}

// 关闭蓝牙连接
void bluetooth_disconnect(uint16_t conn_id)
{
  pServer->disconnect(conn_id);
}

// 蓝牙发送信息（notify 方式）
void bluetooth_notify(uint8_t *message, size_t length)
{
  pReadNotifyCharacteristic->setValue(message, length);
  pReadNotifyCharacteristic->notify();
}
