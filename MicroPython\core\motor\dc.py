from machine import Pin
from utils.time_util import sleep


class DCMotor():
    def __init__(self, pin0, pin1):
        self.__pin0 = Pin(pin0, Pin.OUT)
        self.__pin1 = Pin(pin1, Pin.OUT)

    def __turn(self, s0, s1):
        self.__pin0.value(s0)
        self.__pin1.value(s1)
        return True

    def front_turn(self):
        return self.__turn(1, 0)

    def back_turn(self):
        return self.__turn(0, 1)

    def stop_turn(self):
        return self.__turn(0, 0)

    def turn_motor(self, direction, time=1000):
        status = True
        if direction:
            status = status and self.front_turn()
        else:
            status = status and self.back_turn()
        sleep(time)
        status = status and self.stop_turn()
        return status
