#ifndef HW_S_FPM383_BUSINESS_H
#define HW_S_FPM383_BUSINESS_H

#include "../common.h"
#include "../utils/serial.h"
#include "../utils/esp32_time.h"
#include "../databases/fpm383_database.h"
#include "../databases/operation_database.h"
#include "../controls/fpm383_control.h"
#include "unlock_business.h"

// 指纹业务逻辑流程
void fpm383_process(void (&process_on_delay)());

// 指纹注册回调函数
void fpm383_register_callback(int step);

#endif
