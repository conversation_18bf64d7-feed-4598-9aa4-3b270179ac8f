import sys
import binascii
from core.config.config import Config<PERSON><PERSON><PERSON>
from core.uart.uart import SimpleUART
from utils.hex_util import to_hex_string
from utils.time_util import sleep


class FPM383():
    LED_NONE = 0x00
    LED_GREEN = 0x01
    LED_RED = 0x02
    LED_BLUE = 0x04
    LED_MODE_CLOSE = 0x00
    LED_MODE_OPEN = 0x01
    LED_MODE_TOUCH = 0x02
    LED_MODE_PWM = 0x03
    LED_MODE_FLASH = 0x04

    def __init__(self, rx, tx, index):
        self.__max_fingerprint_num = 40
        self.__config_name = self.__class__.__name__.lower()
        self.__config_helper = ConfigHelper.get_instance()
        self.__init_config()
        self.__uart = SimpleUART(rx=rx, tx=tx, baudrate=57600, index=index, stop=2)

    def __init_config(self, force_clear=False):
        self.__config_helper.create_config([self.__config_name])
        available_id = self.__config_helper.get_configs(self.__config_name, "available_id")
        reference_id = self.__config_helper.get_configs(self.__config_name, "reference_id")
        status = True
        if not isinstance(available_id, list) or force_clear:
            available_id = [x for x in range(1, self.__max_fingerprint_num + 1)]
            if self.__config_helper.set_configs(self.__config_name, "available_id", available_id):
                print("fpm383: config item [available_id] is created!!")
            else:
                status = False
        if not isinstance(reference_id, dict) or force_clear:
            reference_id = {}
            if self.__config_helper.set_configs(self.__config_name, "reference_id", reference_id):
                print("fpm383: config item [reference_id] is created!!")
            else:
                status = False
        return status

    def __get_next_available_id(self):
        available_id = self.__config_helper.get_configs(self.__config_name, "available_id")
        if isinstance(available_id, list) and len(available_id):
            return available_id[0]
        return False

    def __use_id(self, _id):
        available_id = self.__config_helper.get_configs(self.__config_name, "available_id")
        if isinstance(available_id, list) and _id in available_id:
            available_id.remove(_id)
            available_id.sort()
            return self.__config_helper.set_configs(self.__config_name, "available_id", available_id)
        return False

    def __return_id(self, _id):
        available_id = self.__config_helper.get_configs(self.__config_name, "available_id")
        if isinstance(available_id, list) and isinstance(_id, int) and _id not in available_id:
            available_id.append(_id)
            available_id.sort()
            return self.__config_helper.set_configs(self.__config_name, "available_id", available_id)
        return False

    def __change_baudrate(self, rx, tx, index, from_baudrate, to_baudrate):
        body = f"0100050E04{to_hex_string(to_baudrate, 2)}"
        command = self.make_command_v1(body)
        uart_from = SimpleUART(rx=rx, tx=tx, baudrate=from_baudrate * 9600, index=index, stop=2)
        data = uart_from.send_and_receive_hex(command)
        if data and data[-6:-4] == "00":
            return True
        return False

    def create_reference_id(self, ref_id, _id):
        reference_id = self.__config_helper.get_configs(self.__config_name, "reference_id")
        ref_id = str(ref_id)
        if isinstance(reference_id, dict) and not reference_id.get(ref_id):
            reference_id[ref_id] = _id
            return self.__config_helper.set_configs(self.__config_name, "reference_id", reference_id)
        return False

    def remove_reference_id(self, ref_id):
        reference_id = self.__config_helper.get_configs(self.__config_name, "reference_id")
        ref_id = str(ref_id)
        if isinstance(reference_id, dict) and reference_id.get(ref_id):
            _id = reference_id.pop(ref_id)
            if self.__config_helper.set_configs(self.__config_name, "reference_id", reference_id):
                return _id
        return False

    def remove_all_reference_id(self):
        return self.__config_helper.set_configs(self.__config_name, "reference_id", {})

    def make_command_v1(self, body):
        header = "EF01FFFFFFFF"
        body_hex_value = binascii.unhexlify(body)
        body_hex_value = list(body_hex_value)
        body_hex_sum = sum(body_hex_value)
        checksum = to_hex_string(body_hex_sum & 0xFFFF, 4)
        return f"{header}{body}{checksum}"

    def make_command_v2(self, body):
        header = f"F11FE22EB66BA88A{to_hex_string(len(body) // 2 + 1, 4)}"
        header_hex_value = binascii.unhexlify(header)
        header_hex_value = list(header_hex_value)
        header_hex_sum = sum(header_hex_value)
        checksum_header = to_hex_string(0x100 - header_hex_sum % 0x100, 2)
        body_hex_value = binascii.unhexlify(body)
        body_hex_value = list(body_hex_value)
        body_hex_sum = sum(body_hex_value)
        checksum_body = to_hex_string(0x100 - body_hex_sum % 0x100, 2)
        return f"{header}{checksum_header}{body}{checksum_body}"

    def test_command(self, command, delay):
        data = self.__uart.send_and_receive_hex(command, delay=delay)
        return data

    def is_press(self, callback=None):
        try:
            command = "F11FE22EB66BA88A000786000000000135CA"
            data = None
            if callable(callback):
                self.__uart.send_hex(command)
                callback()
                data = self.__uart.receive_hex()
            else:
                data = self.__uart.send_and_receive_hex(command, delay=100)
            if data and data[-4:-2] == "01":
                return True
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def set_led(self, mode, color, p1=0, p2=0, p3=0):
        '''
        color 参数为 LED 颜色，如需同时点亮多个颜色，可以将多个颜色值按位或运算，例如 LED_RED | LED_BLUE\n
        p1 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最大亮度（取值范围 0 - 100）和 LED 每次点亮时长（参数值 * 10ms）\n
        p2 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最小亮度（取值范围 0 - 100）和 LED 每次熄灭时长（参数值 * 10ms）\n
        p3 参数仅当模式为 PWM 或 FLASH 时起效，分别表示亮度每秒变化速率（取值范围 0 - 100）和 LED 闪烁次数\n
        非 PWM 或 FLASH 模式下，p1 / p2 / p3 参数不起效，可以不传入相应参数，取默认值 0\n
        '''
        try:
            body = f"00000000020F{to_hex_string(mode, 2)}{to_hex_string(color, 2)}{to_hex_string(p1, 2)}{to_hex_string(p2, 2)}{to_hex_string(p3, 2)}"
            command = self.make_command_v2(body)
            data = self.__uart.send_and_receive_hex(command, delay=200)
            if data:
                print("----------------")
                print(f"code: {data[-10:-2]}")
                if data[-10:-2] == "00000000":
                    return True
            print("fpm383: set led failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def sleep(self):
        try:
            command = "EF01FFFFFFFF010003330037"
            data = self.__uart.send_and_receive_hex(command, delay=200)
            print("----------------")
            print(f"code: {data[-6:-4]}")
            if data and data[-6:-4] == "00":
                return True
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def auto_register(self, callback=None, try_count=40):
        next_id = self.__get_next_available_id()
        if not next_id:
            print("fpm383: fingerprint library is full!!")
            return False
        try:
            if next_id > 0:
                body = f"01000831{to_hex_string(next_id, 4)}04000B"
                command = self.make_command_v1(body)
                self.__uart.send_hex(command)
                for count in range(try_count):
                    data = self.__uart.receive_hex()
                    if data:
                        data = data.split("ef01ffffffff")
                        data = [d for d in data if d]
                        results = []
                        print("----------------")
                        for d in data:
                            print(f"param1: {d[-8:-6]}, param2: {d[-6:-4]}, code: {d[-10:-8]}")
                            param1 = d[-8:-6]
                            param2 = d[-6:-4]
                            code = d[-10:-8]
                            if code == "00":
                                results.append([param1, param2])
                                if param2 == "f2":
                                    if self.__use_id(next_id):
                                        return next_id
                        if callback and callable(callback):
                            callback(results)
                    sleep(500)
            print("fpm383: register failed or register id error!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def auto_validate(self):
        try:
            command = "EF01FFFFFFFF0100083250FFFF0003028C"
            data = self.__uart.send_and_receive_hex(command, delay=600)
            if data:
                data = data.split("ef01ffffffff")
                data = [d for d in data if d]
                print("----------------")
                for d in data:
                    print(f"param: {d[-14:-12]}, code: {d[-16:-14]}, id: {d[-12:-8]}")
                    param = d[-14:-12]
                    code = d[-16:-14]
                    id_code = d[-12:-8]
                    if param == "05" and code == "00":
                        return id_code
            print("fpm383: validate failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def cancel(self):
        try:
            command = "EF01FFFFFFFF010003300034"
            data = self.__uart.send_and_receive_hex(command, delay=200)
            if data:
                print("----------------")
                print(f"code: {data[-6:-4]}")
                if data[-6:-4] == "00":
                    return True
            print("fpm383: cancel failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def delete_model(self, from_id, count=1):
        try:
            if from_id >= 0 and count > 0:
                body = f"0100070C{to_hex_string(from_id, 4)}{to_hex_string(count, 4)}"
                command = self.make_command_v1(body)
                data = self.__uart.send_and_receive_hex(command)
                if data:
                    print("----------------")
                    print(f"code: {data[-6:-4]}")
                    if data[-6:-4] == "00":
                        status = True
                        for _id in range(from_id, from_id + count):
                            status = self.__return_id(_id) and status
                        return status
            print("fpm383: delete model failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def clear_all_model(self):
        try:
            command = "EF01FFFFFFFF0100030D0011"
            data = self.__uart.send_and_receive_hex(command, delay=5000)
            if data:
                print("----------------")
                print(f"code: {data[-6:-4]}")
                if data[-6:-4] == "00":
                    return self.__init_config(force_clear=True)
            print("fpm383: clear all model failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def load_model(self, _id):
        try:
            command = self.make_command_v1(f"0100060702{to_hex_string(_id, 4)}")
            data = self.__uart.send_and_receive_hex(command)
            if data:
                print("----------------")
                print(f"code: {data[-6:-4]}")
                if data[-6:-4] == "00":
                    return True
            print("fpm383: load model failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def save_model(self):
        next_id = self.__get_next_available_id()
        if not next_id:
            print("fpm383: fingerprint library is full!!")
            return False
        try:
            command = self.make_command_v1(f"0100060601{to_hex_string(next_id, 4)}")
            data = self.__uart.send_and_receive_hex(command)
            if data:
                print("----------------")
                print(f"code: {data[-6:-4]}")
                if data[-6:-4] == "00":
                    if self.__use_id(next_id):
                        return next_id
            print("fpm383: save model failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def upload_model(self):
        try:
            command = self.make_command_v1("0100040802")
            data = ""
            self.__uart.send_hex(command)
            for count in range(1500):
                new_data = self.__uart.receive_hex()
                if new_data:
                    data += new_data
            if data:
                split_datas = data.split("ef01ffffffff")
                split_datas = [f"ef01ffffffff{d}" for d in split_datas if d]
                first_pack = split_datas[0]
                print("----------------")
                print(f"code: {first_pack[-6:-4]}")
                if first_pack[-6:-4] == "00":
                    split_datas = split_datas[1:]
                    last_pack = split_datas[-1]
                    if last_pack[12:14] != "08":
                        print("fpm383: no finish pack detected")
                        return False
                    split_datas = "\n".join(split_datas).strip()
                    return split_datas
            print("fpm383: upload model failed!!")
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def download_model(self, download_datas):
        try:
            command = self.make_command_v1("0100040902")
            data = self.__uart.send_and_receive_hex(command)
            if data:
                print("----------------")
                print(f"code: {data[-6:-4]}")
                if data[-6:-4] == "00":
                    if download_datas:
                        download_datas = download_datas.split("\n")
                        for data in download_datas:
                            if data:
                                self.__uart.send_hex(data)
                                sleep(50)
                                result = self.__uart.receive_hex()
                                print(result)
        except Exception as err:
            sys.print_exception(err)
            return False
