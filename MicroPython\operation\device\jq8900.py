from core.jq8900.jq8900 import JQ8900
from utils.time_util import sleep


class JQ8900Operation():
    def __init__(self, rx, tx, index, volumn=50):
        self.__jq8900 = JQ8900(rx=rx, tx=tx, index=index)
        self.__jq8900.set_volumn(volumn)

    def select_path(self, name):
        path = f"/SOUND*/{name}*???"
        self.__jq8900.select_path(2, path)

    def sound_touch(self, index=1, delay_ratio=1):
        self.select_path(f"TOUCH{index}")
        sleep(int(300 * delay_ratio))

    def sound_lock(self):
        self.select_path("LOCK")
        sleep(1000)

    def sound_unlock(self):
        self.select_path("UNLOCK")
        sleep(1000)

    def sound_error(self):
        self.select_path("ERROR")
        sleep(2000)

    def sound_updating(self):
        self.select_path("UPDING")
        sleep(3000)

    def sound_updated(self):
        self.select_path("UPDOK")
        sleep(2000)

    def sound_updateerror(self):
        self.select_path("UPDERR")
        sleep(2000)
