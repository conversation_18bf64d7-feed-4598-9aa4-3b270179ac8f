#ifndef HW_S_CONFIGURATION_DATABASE_H
#define HW_S_CONFIGURATION_DATABASE_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/file_manager.h"

// 配置项类
class ConfigurationItem
{
public:
  String name;
  String value;

  ConfigurationItem(const String &name = "", const String &value = "");
};

// 配置数据库类
class ConfigurationDatabase
{
public:
  ConfigurationDatabase(const ConfigurationDatabase &) = delete;
  ConfigurationDatabase &operator=(const ConfigurationDatabase &) = delete;

  static ConfigurationDatabase &getInstance();

  bool update(const String &name, const String &value);

  bool remove(const String &name);

  void clear();

  bool getConfig(const String &name, String &value) const;

  void load();

  void store();

private:
  static const int MAX_CONFIGS = 50;
  ConfigurationItem configs[MAX_CONFIGS];
  int numConfigs;

  ConfigurationDatabase();

  void loadJson(const String &json_string);

  void toJson(String &json_string) const;
};

#endif
