from esp32 import U<PERSON>, wake_on_ulp
from machine import mem32
from esp32_ulp import src_to_binary
import time

source = """\
#define DR_REG_RTCIO_BASE            0x3ff48400
#define RTC_IO_TOUCH_PAD2_REG        (DR_REG_RTCIO_BASE + 0x9c)
#define RTC_IO_TOUCH_PAD2_MUX_SEL_M  (BIT(19))
#define RTC_IO_TOUCH_PAD2_FUN_IE_M   (BIT(13))
#define RTC_IO_TOUCH_PAD7_REG        (DR_REG_RTCIO_BASE + 0xb0)
#define RTC_IO_TOUCH_PAD7_MUX_SEL_M  (BIT(19))
#define RTC_IO_TOUCH_PAD7_FUN_IE_M   (BIT(13))
#define RTC_GPIO_IN_REG              (DR_REG_RTCIO_BASE + 0x24)
#define RTC_GPIO_IN_NEXT_S           14

state:      .long 0

entry:
            WRITE_RTC_REG(RTC_IO_TOUCH_PAD2_REG, RTC_IO_TOUCH_PAD2_MUX_SEL_M, 1, 1)
            WRITE_RTC_REG(RTC_IO_TOUCH_PAD2_REG, RTC_IO_TOUCH_PAD2_FUN_IE_M, 1, 1)
            WRITE_RTC_REG(RTC_IO_TOUCH_PAD7_REG, RTC_IO_TOUCH_PAD7_MUX_SEL_M, 1, 1)
            WRITE_RTC_REG(RTC_IO_TOUCH_PAD7_REG, RTC_IO_TOUCH_PAD7_FUN_IE_M, 1, 1)
            READ_RTC_REG(RTC_GPIO_IN_REG, RTC_GPIO_IN_NEXT_S + 12, 1)
            jumpr wakeup, 0, eq
            READ_RTC_REG(RTC_GPIO_IN_REG, RTC_GPIO_IN_NEXT_S + 17, 1)
            jumpr wakeup, 1, eq
            halt

wakeup:
            wake
            halt
"""


def start():
    wake_on_ulp(True)
    binary = src_to_binary(source, cpu="esp32")
    load_addr, entry_addr = 0, 4
    ULP_MEM_BASE = 0x50000000
    ULP_DATA_MASK = 0xffff
    ulp = ULP()
    ulp.set_wakeup_period(0, 50000)
    ulp.load_binary(load_addr, binary)
    mem32[ULP_MEM_BASE + load_addr] = 0x0
    ulp.run(entry_addr)
