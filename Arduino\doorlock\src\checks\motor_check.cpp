#include "../../include/checks/motor_check.h"

// 电机异常检测及处理
void check_motor_exception()
{
  int pin0_status = digitalRead(PIN_MOTOR_0);
  int pin1_status = digitalRead(PIN_MOTOR_1);
  if (pin0_status ^ pin1_status)
  {
    if (pin0_status == HIGH && pin1_status == LOW)
    {
      // 正转异常
      print_log("exception", "motor", "front turn exception!!");
      digitalWrite(PIN_MOTOR_0, LOW);
      digitalWrite(PIN_MOTOR_1, LOW);
    }
    else if (pin0_status == LOW && pin1_status == HIGH)
    {
      // 反转异常
      print_log("exception", "motor", "back turn exception!!");
      digitalWrite(PIN_MOTOR_0, LOW);
      digitalWrite(PIN_MOTOR_1, LOW);
    }
    led_blink(PIN_LED_LOW_BATTERY, 100, 100, 6);
  }
}
