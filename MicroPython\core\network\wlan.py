import sys
import network
from core.config.config import ConfigHelper


class WLAN():
    __instance = None

    def __init__(self):
        try:
            self.__wlan = network.WLAN(network.STA_IF)
            self.__wlan.active(True)
            self.__config_name = self.__class__.__name__.lower()
            self.__config_helper = ConfigHelper.get_instance()
            self.__init_config()
            self.__wlan_name = self.__config_helper.get_configs(self.__config_name, "wlan_name")
            self.__wlan_password = self.__config_helper.get_configs(self.__config_name, "wlan_password")
        except Exception as err:
            sys.print_exception(err)

    def __init_config(self):
        self.__config_helper.create_config([self.__config_name])

    @classmethod
    def get_instance(cls, *args, **kwargs):
        if not cls.__instance:
            cls.__instance = cls(*args, **kwargs)
        return cls.__instance

    def get_config(self):
        if self.__wlan.isconnected():
            return self.__wlan.ifconfig()
        else:
            return False

    def connect(self):
        try:
            if isinstance(self.__wlan_name, str) and isinstance(self.__wlan_password, str):
                if self.__wlan.isconnected():
                    print("wlan already connected!!")
                else:
                    self.__wlan.connect(self.__wlan_name, self.__wlan_password)
                    print("try to connect wlan!!")
            else:
                print("wlan config error!!")
                return False
        except Exception as err:
            sys.print_exception(err)

    def disconnect(self):
        try:
            if self.__wlan.isconnected():
                self.__wlan.disconnect()
        except Exception as err:
            sys.print_exception(err)
