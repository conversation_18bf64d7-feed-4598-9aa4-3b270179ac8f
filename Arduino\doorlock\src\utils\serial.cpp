#include "../../include/utils/serial.h"

// 初始化串口
void serial_init()
{
  Serial.begin(SERIAL_BASE_BAUDRATE);
  // VoiceSerial.begin(SERIAL_VOICE_BAUDRATE, SERIAL_VOICE_CONFIG, SERIAL_VOICE_RX, SERIAL_VOICE_TX);
  // FPM383Serial.begin(SERIAL_FPM383_BAUDRATE, SERIAL_FPM383_CONFIG, SERIAL_FPM383_RX, SERIAL_FPM383_TX);
}

// 语音串口写入
void voice_write(const char *message, size_t size, uint32_t delay_time)
{
  // VoiceSerial.write(message, size);
  // delay(delay_time);
}

// 指纹串口读取
int fpm383_read(char *received_data, int size)
{
  // int data_index = 0;
  // memset(received_data, 0, size);
  // while (FPM383Serial.available() > 0)
  // {
  //   char received_byte = FPM383Serial.read();
  //   if (data_index < size - 1)
  //   {
  //     received_data[data_index] = received_byte;
  //     data_index++;
  //   }
  //   else
  //   {
  //     break;
  //   }
  // }
  // return data_index;
  return 0;
}

// 指纹串口写入
void fpm383_write(const char *message, size_t size, uint32_t delay_time)
{
  // FPM383Serial.write(message, size);
  // delay(delay_time);
}
