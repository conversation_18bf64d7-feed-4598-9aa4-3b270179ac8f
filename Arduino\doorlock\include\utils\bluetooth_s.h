#ifndef HW_S_BLUETOOTH_S_H
#define HW_S_BLUETOOTH_S_H

#include "../common.h"
#include "utils.h"
#include <BLEDevice.h>
#include <BLEServer.h>
#include <BLEUtils.h>
#include <BLE2902.h>

// 初始化蓝牙
void bluetooth_init(BLEServerCallbacks *serverCallbacks, BLECharacteristicCallbacks *characteristicCallbacks);

// 关闭蓝牙连接
void bluetooth_disconnect(uint16_t conn_id);

// 蓝牙发送信息（notify 方式）
void bluetooth_notify(uint8_t *message, size_t length);

#endif
