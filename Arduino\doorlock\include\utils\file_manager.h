#ifndef HW_S_FILE_MANAGER_H
#define HW_S_FILE_MANAGER_H

#include "../common.h"
#include "utils.h"
#include <LittleFS.h>

// 文件管理类
class FileManager
{
public:
  FileManager(const char *fileName, const char *default_value);

  size_t getSize();

  bool write(const char *content);

  bool read(char *content, size_t size);

  bool readString(String &content, size_t size);

private:
  const char *fileName;

  bool exists();
};

#endif
