#include "../../include/utils/ota.h"

// WebServer
WebServer server(80);

// OTA 更新
void start_ota_update()
{
  if (wifi_is_connected())
  {
    const char host[] = "esp32";
    if (!MDNS.begin(host))
    {
      print_log("ota", "mdns", "mdns start failed!!");
    }
    server.on("/", HTTP_GET, []()
              {
    server.sendHeader("Connection", "close");
           server.send(200, "text/html", OTA_UPLOAD_PAGE); });
    server.on(
        "/update", HTTP_POST, []()
        {
    server.sendHeader("Connection", "close");
    server.send(200, "text/plain", (Update.hasError()) ? "FAIL" : "OK");
    ESP.restart(); },
        []()
        {
          HTTPUpload &upload = server.upload();
          if (upload.status == UPLOAD_FILE_START)
          {
            print_log("ota", "update", "update started!!");
            if (!Update.begin(UPDATE_SIZE_UNKNOWN))
            {
              Update.printError(Serial);
            }
          }
          else if (upload.status == UPLOAD_FILE_WRITE)
          {
            if (Update.write(upload.buf, upload.currentSize) != upload.currentSize)
            {
              Update.printError(Serial);
            }
          }
          else if (upload.status == UPLOAD_FILE_END)
          {
            if (Update.end(true))
            {
              print_log("ota", "update", "update successed!!");
            }
            else
            {
              Update.printError(Serial);
            }
          }
        });
    server.begin();
    for (int t = 0; t < 900; t++)
    {
      server.handleClient();
      delay(100);
    }
  }
}