#ifndef HW_S_KEYPAD_CONTROL_H
#define HW_S_KEYPAD_CONTROL_H

#include "../common.h"
#include "../utils/utils.h"
#include "../databases/keypad_database.h"
#include <Wire.h>

// 按键读取结果结构体
struct KeypadReadResult
{
  bool isPress;
  bool isMatch;
  bool isSharp;
  int userId;
  String record10;

  KeypadReadResult() : isPress(false), isMatch(false), isSharp(false), userId(0), record10(""){};
};

// 按键初始化
void keypad_init();

// 设置按键睡眠模式
void keypad_set_sleep_mode(int mode);

// 设置按键空闲时间
void keypad_set_idle_time(int time, int time_suffix);

// 按键验证流程
KeypadReadResult keypad_read_process();

#endif
