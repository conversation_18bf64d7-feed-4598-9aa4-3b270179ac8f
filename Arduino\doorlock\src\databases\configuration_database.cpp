#include "../../include/databases/configuration_database.h"

// public (ConfigurationItem)

ConfigurationItem::ConfigurationItem(const String &name, const String &value) : name(name), value(value) {}

// public (ConfigurationDatabase)

ConfigurationDatabase &ConfigurationDatabase::getInstance()
{
  static ConfigurationDatabase configDB;
  return configDB;
}

bool ConfigurationDatabase::update(const String &name, const String &value)
{
  for (int t = 0; t < numConfigs; ++t)
  {
    if (configs[t].name == name)
    {
      configs[t].value = value;
      return true;
    }
  }
  if (numConfigs < MAX_CONFIGS)
  {
    configs[numConfigs].name = name;
    configs[numConfigs].value = value;
    numConfigs++;
    return true;
  }
  else
  {
    print_log("configuration", "update", "number of configs exceeds the maximum limit!!");
    return false;
  }
}

bool ConfigurationDatabase::remove(const String &name)
{
  for (int t0 = 0; t0 < numConfigs; t0++)
  {
    if (configs[t0].name == name)
    {
      for (int t1 = t0; t1 < numConfigs - 1; t1++)
      {
        configs[t1].name = configs[t1 + 1].name;
        configs[t1].value = configs[t1 + 1].value;
      }
      numConfigs--;
      return true;
    }
  }
  print_log("configuration", "remove", "no config found!!");
  return false;
}

void ConfigurationDatabase::clear()
{
  for (int t = 0; t < numConfigs; t++)
  {
    configs[t].name = "";
    configs[t].value = "";
  }
  numConfigs = 0;
}

bool ConfigurationDatabase::getConfig(const String &name, String &value) const
{
  for (int t = 0; t < numConfigs; ++t)
  {
    if (configs[t].name == name)
    {
      value = configs[t].value;
      return true;
    }
  }
  return false;
}

void ConfigurationDatabase::load()
{
  FileManager fileManager(FILE_NAME_CONFIG, FILE_DEFAULT_CONFIG);
  String config_string;
  if (fileManager.readString(config_string, fileManager.getSize()))
  {
    loadJson(config_string);
  }
}

void ConfigurationDatabase::store()
{
  FileManager fileManager(FILE_NAME_CONFIG, FILE_DEFAULT_CONFIG);
  String config_string;
  toJson(config_string);
  fileManager.write(config_string.c_str());
}

// private (ConfigurationDatabase)

ConfigurationDatabase::ConfigurationDatabase() : numConfigs(0)
{
  load();
}

void ConfigurationDatabase::loadJson(const String &json_string)
{
  size_t size = get_json_size(100, 1, json_string.length());
  DynamicJsonDocument doc(size);
  DeserializationError error = deserializeJson(doc, json_string);
  if (error)
  {
    print_log("configuration", "loadJson", "deserialization error!!");
    return;
  }
  JsonArray configsArray = doc.as<JsonArray>();
  for (JsonObject configJson : configsArray)
  {
    if (configJson["name"].isNull() || configJson["value"].isNull())
    {
      print_log("configuration", "loadJson", "error config format!!");
    }
    else
    {
      String name = configJson["name"];
      String value = configJson["value"];
      update(name, value);
    }
  }
}

void ConfigurationDatabase::toJson(String &json_string) const
{
  size_t size = get_json_size(numConfigs * 2, 1, numConfigs * 60);
  DynamicJsonDocument doc(size);
  JsonArray configsArray = doc.to<JsonArray>();
  for (int t = 0; t < numConfigs; t++)
  {
    JsonObject configJson = configsArray.createNestedObject();
    configJson["name"] = configs[t].name;
    configJson["value"] = configs[t].value;
  }
  serializeJson(doc, json_string);
}
