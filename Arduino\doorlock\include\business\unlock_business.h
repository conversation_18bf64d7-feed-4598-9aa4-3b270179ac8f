#ifndef HW_S_UNLOCK_BUSINESS_H
#define HW_S_UNLOCK_BUSINESS_H

#include "../common.h"
#include "../utils/serial.h"
#include "../databases/configuration_database.h"
#include "../controls/motor_control.h"

struct MotorConfig
{
  String direction_1;
  String direction_2;
  String time_1;
  String time_2;
  String off_time;
};

// 获取电机配置参数
MotorConfig get_motor_config();

// 设置电机配置参数
bool set_motor_config(MotorConfig config);

// 验证成功流程
bool on_unlock();

// 验证失败流程
void on_error();

#endif
