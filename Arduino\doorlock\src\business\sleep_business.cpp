#include "../../include/business/sleep_business.h"

// 睡眠前逻辑
void before_sleep()
{
// 设置唤醒源
#ifdef CORE_ESP32
  esp_sleep_enable_ext0_wakeup(PIN_WAKEUP_FPM383, HIGH);
  esp_sleep_enable_ext1_wakeup(BIT64(PIN_WAKEUP_KEYPAD), ESP_EXT1_WAKEUP_ALL_LOW);
#endif
#ifdef CORE_ESP32_C3
  esp_deep_sleep_enable_gpio_wakeup(BIT(PIN_WAKEUP_BUTTON), ESP_GPIO_WAKEUP_GPIO_HIGH);
#endif
  // 设置按键空闲模式
  // keypad_set_idle_time(0xFF, 0xFF);
  // 关闭指纹灯光
  // fpm383_set_led(0x00, 0x00);
  delay(100);
  // 指纹取消 + 睡眠
  // fpm383_cancel();
  // fpm383_sleep();
  // 关闭蓝牙
  if (bluetooth_is_connected())
  {
    bluetooth_disconnect(get_bluetooth_connect_id());
  }
  // 关闭 WiFi
  WiFi.disconnect(true, true);
  delay(500);
  // 设置引脚模式
  pinMode(PIN_WAKEUP_BUTTON, INPUT);
  // pinMode(PIN_WAKEUP_FPM383, INPUT);
  // pinMode(PIN_WAKEUP_KEYPAD, INPUT);
  pinMode(SERIAL_BASE_RX, INPUT);
  pinMode(SERIAL_BASE_TX, INPUT);
  // pinMode(SERIAL_VOICE_RX, INPUT);
  // pinMode(SERIAL_VOICE_TX, INPUT);
  // pinMode(SERIAL_FPM383_RX, INPUT);
  // pinMode(SERIAL_FPM383_TX, INPUT);
  // 设置引脚低电平
  digitalWrite(PIN_LED_BLUETOOTH, LOW);
  digitalWrite(PIN_LED_LOW_BATTERY, LOW);
  // digitalWrite(PIN_POWER, LOW);
}
