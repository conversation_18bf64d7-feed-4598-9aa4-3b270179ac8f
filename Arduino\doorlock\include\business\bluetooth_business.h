#ifndef HW_S_BLUETOOTH_BUSINESS_H
#define HW_S_BLUETOOTH_BUSINESS_H

#include "../common.h"
#include "../utils/serial.h"
#include "../utils/esp32_time.h"
#include "../utils/utils.h"
#include "../utils/utils_f.h"
#include "../utils/bluetooth_s.h"
#include "../utils/aes_ctr.h"
#include "../databases/initialize.h"
#include "../databases/configuration_database.h"
#include "../databases/keypad_database.h"
#include "../databases/fpm383_database.h"
#include "../databases/operation_database.h"
#include "../controls/sleep_control.h"
#include "../controls/fpm383_control.h"
#include "../controls/bluetooth_control.h"
#include "unlock_business.h"
#include "fpm383_business.h"

// 蓝牙数据分析处理
void bluetooth_message_analysis(std::string &message);

#endif
