#include "../../include/databases/initialize.h"

// 初始化配置文件
void initialize_config()
{
  ConfigurationDatabase::getInstance().clear();
  ConfigurationDatabase::getInstance().store();
}

// 初始化 Counter
void initialize_counter()
{
  Counter::getInstance().removeAll();
  Counter::getInstance().store();
}

// 初始化按键相关内容
void initialize_keypad()
{
  KeypadDatabase::getInstance().clear();
  KeypadDatabase::getInstance().store();
}

// 初始化指纹相关内容
void initialize_fpm383()
{
  FPM383Database::getInstance().clear();
  FPM383Database::getInstance().store();
  fpm383_clear();
}

// 初始化操作记录
void initialize_operation()
{
  OperationDatabase::getInstance().clear();
  OperationDatabase::getInstance().store();
}

// 初始化 BluetoothNote
void initialize_bluetooth_note()
{
  FileManager fileManager(FILE_NAME_BLUETOOTH_NOTE, FILE_DEFAULT_BLUETOOTH_NOTE);
  fileManager.write(FILE_DEFAULT_BLUETOOTH_NOTE);
}

// 初始化所有内容
void initialize_all()
{
  initialize_config();
  initialize_counter();
  initialize_keypad();
  initialize_fpm383();
  initialize_operation();
  initialize_bluetooth_note();
}
