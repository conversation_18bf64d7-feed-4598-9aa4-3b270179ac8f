#include "../../include/business/keypad_business.h"

// 按键业务逻辑流程
void keypad_process()
{
  // 请勿随意修改循环次数，否则可能造成指纹验证异常
  for (int t = 0; t < 80; t++)
  {
    KeypadReadResult result = keypad_read_process();
    if (result.isPress)
    {
      sleep_report();
      if (result.isSharp)
      {
        if (result.isMatch)
        {
          bool ok = on_unlock();
          if (ok)
          {
            OperationDatabase::getInstance().add(result.userId, OPERATION_TYPE_KEYPAD, esp32_get_time());
            OperationDatabase::getInstance().store();
          }
        }
        else
        {
          on_error();
        }
      }
      else
      {
        voice_write(VOICE_TOUCH, sizeof(VOICE_TOUCH), 0);
      }
    }
  }
}
