import sys
import binascii
from machine import UART
from utils.time_util import sleep


class SimpleUART():
    def __init__(self, rx, tx, baudrate, index, timeout=10, stop=1):
        self.__uart = UART(index, baudrate=baudrate, rx=rx, tx=tx, timeout=timeout, stop=stop)

    def send(self, message):
        try:
            self.receive()
            self.__uart.write(message)
            return True
        except Exception as err:
            sys.print_exception(err)
            return False

    def send_hex(self, message):
        try:
            return self.send(binascii.unhexlify(message))
        except Exception as err:
            sys.print_exception(err)
            return False

    def receive(self):
        try:
            data = self.__uart.read()
            return data
        except Exception as err:
            sys.print_exception(err)
            return False

    def receive_hex(self):
        try:
            data = self.receive()
            if data:
                data = binascii.hexlify(data)
                data = data.decode()
                return data
        except Exception as err:
            sys.print_exception(err)
            return False

    def send_and_receive_hex(self, message, delay=500):
        try:
            self.send_hex(message)
            sleep(delay)
            data = self.receive_hex()
            return data
        except Exception as err:
            sys.print_exception(err)
            return False
