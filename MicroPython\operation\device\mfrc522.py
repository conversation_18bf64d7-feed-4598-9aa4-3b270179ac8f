from core.mfrc522.mfrc522 import MFRC522


class MFRC522Operation():
    def __init__(self, sck, sda, mosi, miso, rst):
        self.__mfrc522 = MFRC522(
            sck=sck, sda=sda, mosi=mosi, miso=miso, rst=rst)

    def create_available_card(self, _id, uid, key):
        return self.__mfrc522.create_available_card(
            _id=_id,
            uid=uid,
            key=key
        )

    def remove_available_card(self, _id):
        return self.__mfrc522.remove_available_card(_id=_id)

    def change_key(self, old_key_B, new_key_A, new_key_B):
        return self.__mfrc522.change_key(
            old_key_B=old_key_B,
            new_key_A=new_key_A,
            new_key_B=new_key_B,
            area=3
        )

    def read(self):
        return self.__mfrc522.auto_read(
            area=3,
            block=0
        )

    def write(self, key_A, data):
        return self.__mfrc522.auto_write(
            key_A=key_A,
            data=data,
            area=3,
            block=0
        )
