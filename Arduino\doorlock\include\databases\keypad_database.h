#ifndef HW_S_KEYPAD_DATABASE_H
#define HW_S_KEYPAD_DATABASE_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/file_manager.h"

// 按键数据类
class KeypadRecord
{
public:
  int userId;
  String password;

  KeypadRecord(int id = 0, const String &pwd = "");
};

// 按键数据库类
class KeypadDatabase
{
public:
  KeypadDatabase(const KeypadDatabase &) = delete;
  KeypadDatabase &operator=(const KeypadDatabase &) = delete;

  static KeypadDatabase &getInstance();

  bool add(int userId, const String &password);

  bool remove(int userId, const String &password);

  void clear();

  void findByPassword(const String &password, std::vector<KeypadRecord> &matching_record) const;

  void load();

  void store();

private:
  static const int MAX_RECORDS = 100;
  KeypadRecord records[MAX_RECORDS];
  int numRecords;

  KeypadDatabase();

  void loadJson(const String &json_string);

  void toJson(String &json_string) const;
};

#endif
