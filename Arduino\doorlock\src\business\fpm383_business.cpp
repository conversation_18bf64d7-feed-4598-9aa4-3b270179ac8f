#include "../../include/business/fpm383_business.h"

// 指纹录入进度
int fpm383_register_process_num = 0xff;

// 指纹业务逻辑流程
void fpm383_process(void (&process_on_delay)())
{
  FPM383VerifyResult result = fpm383_verify_process(process_on_delay);
  if (result.isPress)
  {
    if (result.isPass)
    {
      fpm383_set_led(0x04, 0x01, 10, 10, 5);
      bool ok = on_unlock();
      if (ok)
      {
        OperationDatabase::getInstance().add(result.userId, OPERATION_TYPE_FPM383, esp32_get_time());
        OperationDatabase::getInstance().store();
      }
    }
    else
    {
      fpm383_set_led(0x04, 0x02, 10, 10, 5);
      on_error();
    }
    fpm383_set_led(0x03, 0x04, 100, 0, 60);
  }
}

// 指纹注册回调函数
void fpm383_register_callback(int step)
{
  if (step <= 0x03 && step != fpm383_register_process_num)
  {
    if (step == 0x00)
    {
      voice_write(VOICE_FPM383_START, sizeof(VOICE_FPM383_START), 0);
    }
    else
    {
      voice_write(VOICE_FPM383_AGAIN, sizeof(VOICE_FPM383_AGAIN), 0);
    }
    fpm383_register_process_num = step;
  }
}
