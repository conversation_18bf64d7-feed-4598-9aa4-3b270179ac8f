#ifndef HW_S_OPERATION_DATABASE_H
#define HW_S_OPERATION_DATABASE_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/file_manager.h"

// 操作记录数据类
class OperationRecord
{
public:
  int uid;
  int type;
  time_t timestamp;

  OperationRecord(int uid = 0, int type = 0, time_t timestamp = 0);
};

// 操作记录数据库类
class OperationDatabase
{
public:
  OperationDatabase(const OperationDatabase &) = delete;
  OperationDatabase &operator=(const OperationDatabase &) = delete;

  static OperationDatabase &getInstance();

  bool add(int uid, int type, time_t timestamp);

  bool remove(int index);

  void clear();

  bool get(int index, OperationRecord &record);

  void load();

  void store();

private:
  static const int MAX_RECORDS = 50;
  OperationRecord records[MAX_RECORDS];
  int numRecords;

  OperationDatabase();

  void loadJson(const String &json_string);

  void toJson(String &json_string) const;
};

#endif
