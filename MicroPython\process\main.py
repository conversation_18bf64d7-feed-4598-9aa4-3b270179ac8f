from machine import reset, Pin
from core.config.config import Confi<PERSON><PERSON><PERSON><PERSON>
from operation.index import Operation
from process.mqtt import publish
from utils.time_util import sleep


# LED
led = Pin(13, Pin.OUT)
led.on()
config_helper = ConfigHelper.get_instance()
operation = Operation.get_instance()
unlock_status = False
normally_status = config_helper.get_configs("states", "normally_status")


def change_normally_status():
    global normally_status
    global unlock_status
    if normally_status:
        print("normally close!!")
        operation.dc.turn(True, 500)
        operation.jq8900.sound_lock()
        normally_status = False
        unlock_status = False
        config_helper.set_configs("states", "normally_status", False)
    elif unlock_status:
        print("normally open!!")
        normally_status = True
        config_helper.set_configs("states", "normally_status", True)
        reset()


operation.gtx314l.add_listener("******", change_normally_status)


def unlock():
    global unlock_status
    operation.dc.turn(False, 500)
    operation.jq8900.sound_unlock()
    unlock_status = True
    # publish("unlock")
    for _ in range(25):
        operation.gtx314l.record_key_continuous()
        sleep(200)
    if not normally_status:
        operation.dc.turn(True, 500)
        operation.jq8900.sound_lock()
        unlock_status = False


def error():
    operation.jq8900.sound_error()


def mfrc522_read():
    if not normally_status:
        result = operation.mfrc522.read()
        if result:
            unlock()
        return result
    return False


def gtx314l_read():
    if not normally_status:
        result = operation.gtx314l.record_key()
        if result:
            operation.jq8900.sound_touch(index=1, delay_ratio=0)
        if isinstance(result, int):
            if result > 0:
                unlock()
            else:
                error()
        return result
    else:
        result = operation.gtx314l.record_key_continuous()
        sleep(200)
        return result


def fpm383_read(callback):
    if not normally_status:
        if operation.fpm383.is_press(callback=callback):
            result = operation.fpm383.validate()
            if result:
                operation.fpm383.open_led_success()
                unlock()
            else:
                operation.fpm383.open_led_fail()
                error()
            operation.fpm383.open_led_base()
            return result
    return False
