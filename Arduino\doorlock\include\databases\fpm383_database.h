#ifndef HW_S_FPM383_DATABASE_H
#define HW_S_FPM383_DATABASE_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/file_manager.h"

// FPM383 数据类
class FPM383Record
{
public:
  bool used;
  int uid;

  FPM383Record();
};

// FPM383 数据库类
class FPM383Database
{
public:
  FPM383Database(const FPM383Database &) = delete;
  FPM383Database &operator=(const FPM383Database &) = delete;

  static FPM383Database &getInstance();

  bool add(int number, int uid);

  bool remove(int number);

  void clear();

  int getNext();

  int getNumberByUid(int uid);

  int getUidByNumber(int number);

  void load();

  void store();

private:
  static const int MAX_RECORDS = 50;
  FPM383Record records[MAX_RECORDS + 1];

  FPM383Database();

  void loadJson(const String &json_string);

  void toJson(String &json_string) const;
};

#endif
