#ifndef HW_S_UTILS_H
#define HW_S_UTILS_H

#include "../common.h"

// 检查一个字符串是否是数字字符串
bool is_numeric_string(String &str);

// 比较两个字符数组是否完全相同
bool compare_array(const char *c1, const char *c2, int size);

// 十六进制字符串转换字符数组
void hex_string_to_char_array(String &hex_string, char *char_array, int length);

// 数值转十六进制字符串，可指定长度
void number_to_hex_string(int number, int length, String &hex_string);

// 打印十六进制字符
void print_hex(char hex_char);

// 行内打印十六进制字符数组
void print_hex_inline(const char *data, size_t length);

// 打印日志信息
void print_log(const String &title, const String &desc, const String &message);

// 获取随机数
uint32_t get_random_number();

// 生成随机 uint8_t 数组
void generate_random_uint8(uint8_t *data, size_t length);

// 生成随机字符串
void generate_random_string(size_t length, bool include_digits, bool include_lowercase, bool include_uppercase, String &str);

// 获取时间差
unsigned long get_time_diff(unsigned long time);

// 获取设备电量百分比
float get_battery_percent(uint8_t pin, int full_charged_value, int empty_value);

// LED 闪烁
void led_blink(uint8_t pin, int on_time, int off_time, int num_flashes);

// LED 闪烁（使用计数器）
void led_blink_using_counter(uint8_t pin, unsigned long on_count, unsigned long off_count, unsigned long counter);

// 获取唯一标识，unique_id 的长度建议至少为 18 位
void get_unique_id(char *unique_id);

// 获取设备标识，entity_id 的长度建议至少为 50 位
void get_entity_id(char *entity_id);

// 获取设备名称，device_name 的长度建议至少为 50 位
void get_device_name(char *device_name);

// 获取蓝牙名称，bluetooth_name 的长度建议至少为 50 位
void get_bluetooth_name(char *bluetooth_name);

// 获取专有 MQTT 主题名称，mqtt_topic_self 的长度建议至少为 50 位
void get_mqtt_topic_self(char *mqtt_topic_self);

// 生成指纹命令 v1
void get_fpm383_command_v1(char *command, const char *body, int body_length);

// 生成指纹命令 v2
void get_fpm383_command_v2(char *command, const char *body, int body_length);

/**
 * 获取指纹 LED 命令
 *
 * LED 模式取值：关闭 0x00  开启 0x01  按下发光（不可用） 0x02  PWM渐变 0x03  FLASH闪烁 0x04
 * LED 颜色取值：绿色 0x01  红色 0x02  蓝色 0x04
 *
 * color 参数为 LED 颜色，如需同时点亮多个颜色，可以将多个颜色值按位或运算，例如 0x01 | 0x02
 * p1 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最大亮度（取值范围 0 - 100）和 LED 每次点亮时长（参数值 * 10ms）
 * p2 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最小亮度（取值范围 0 - 100）和 LED 每次熄灭时长（参数值 * 10ms）
 * p3 参数仅当模式为 PWM 或 FLASH 时起效，分别表示亮度每秒变化速率（取值范围 0 - 100）和 LED 闪烁次数
 * 非 PWM 或 FLASH 模式下，p1 / p2 / p3 参数不起效，可以不传入相应参数，取默认值 0
 */
void get_fpm383_command_led(char *command, char mode, char color, char p1 = 0, char p2 = 0, char p3 = 0);

// 获取指纹注册命令
void get_fpm383_command_register(char *command, int number);

// 获取指纹储存模板命令
void get_fpm383_command_store(char *command, int number);

// 获取指纹删除命令
void get_fpm383_command_remove(char *command, int number);

#endif
