from operation.device.fpm383 import FPM383Operation
from operation.device.dc import DCOperation
from operation.device.gtx314l import GTX314LOperation
from operation.device.jq8900 import JQ8900Operation


class Operation():
    __instance = None

    def __init__(self):
        self.gtx314l = GTX314LOperation(scl=18, sda=5, addr=88)
        self.jq8900 = JQ8900Operation(rx=4, tx=16, index=1)
        self.fpm383 = FPM383Operation(rx=23, tx=22, index=2)
        self.dc = DCOperation(pin0=26, pin1=25)

    @classmethod
    def get_instance(cls, *args, **kwargs):
        if not cls.__instance:
            cls.__instance = cls(*args, **kwargs)
        return cls.__instance
