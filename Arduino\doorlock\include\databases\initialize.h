#ifndef HW_S_INITIALIZE_H
#define HW_S_INITIALIZE_H

#include "../common.h"
#include "../utils/file_manager.h"
#include "configuration_database.h"
#include "counter.h"
#include "fpm383_database.h"
#include "keypad_database.h"
#include "operation_database.h"
#include "../controls/fpm383_control.h"

// 初始化配置文件
void initialize_config();

// 初始化 Counter
void initialize_counter();

// 初始化按键相关内容
void initialize_keypad();

// 初始化指纹相关内容
void initialize_fpm383();

// 初始化操作记录
void initialize_operation();

// 初始化 BluetoothNote
void initialize_bluetooth_note();

// 初始化所有内容
void initialize_all();

#endif
