import binascii
from core.config.config import Config<PERSON><PERSON><PERSON>
from core.mfrc522.mfrc522_api import MFRC522Api


class MFRC522():
    def __init__(self, sck, sda, mosi, miso, rst):
        self.__api = MFRC522Api(sck=sck, mosi=mosi, miso=miso, rst=rst, cs=sda)
        self.__available_card = {}
        self.__config_name = self.__class__.__name__.lower()
        self.__config_helper = ConfigHelper.get_instance()
        self.__init_config()
        self.__read_available_card()

    def __init_config(self, force_clear=False):
        self.__config_helper.create_config([self.__config_name])
        available_card = self.__config_helper.get_configs(self.__config_name, "available_card")
        status = True
        if not isinstance(available_card, dict) or force_clear:
            available_card = {}
            if self.__config_helper.set_configs(self.__config_name, "available_card", available_card):
                print("mfrc522: config item [available_card] is created!!")
            else:
                status = False
        return status

    def __read_available_card(self):
        available_card = self.__config_helper.get_configs(self.__config_name, "available_card")
        if isinstance(available_card, dict):
            self.__available_card = available_card
            return True
        return False

    def __check_card(self):
        stat, _ = self.__api.request(MFRC522Api.REQIDL)
        if stat == MFRC522Api.OK:
            stat, raw_uid = self.__api.anticoll()
            if stat == MFRC522Api.OK:
                if self.__api.select_tag(raw_uid) == MFRC522Api.OK:
                    return raw_uid
        return False

    def __auth_A(self, block_index, key, uid):
        return self.__api.auth(MFRC522Api.AUTHENT1A, block_index, key, uid) == MFRC522Api.OK

    def __auth_B(self, block_index, key, uid):
        return self.__api.auth(MFRC522Api.AUTHENT1B, block_index, key, uid) == MFRC522Api.OK

    def __read(self, block_index):
        if block_index % 4 != 3:
            data = self.__api.read(block_index)
            if data and isinstance(data, list) and len(data) == 16:
                self.__finish()
                return data
        return False

    def __read_config(self, block_index):
        if block_index % 4 == 3:
            data = self.__api.read(block_index)
            if data and isinstance(data, list) and len(data) == 16:
                self.__finish()
                return data
        return False

    def __write(self, block_index, data):
        if block_index % 4 != 3 and len(data) == 16:
            data = bytes(data)
            result = self.__api.write(block_index, data) == MFRC522Api.OK
            self.__finish()
            return result
        return False

    def __write_config(self, block_index, key_A, key_B):
        '''
        控制权限默认设置为 7F 07 88 (数据块 C1 = C2 = C3 = 0，存取控制块 C1 = 0，C2 = C3 = 1) \n
        数据块 key_A / key_B 可读写 \n
        存取控制块 key_B 可写密码和修改控制权限，key_A / key_B 可读取控制权限 \n
        控制权限说明见 source 文件夹 \n
        '''
        if block_index % 4 == 3 and len(key_A) == 6 and len(key_B) == 6:
            data = []
            data.extend(key_A)
            data.extend([0x7F, 0x07, 0x88, 0x00])
            data.extend(key_B)
            data = bytes(data)
            result = self.__api.write(block_index, data) == MFRC522Api.OK
            self.__finish()
            return result
        return False

    def __finish(self):
        self.__api.stop_crypto1()

    def create_available_card(self, _id, uid, key):
        available_card = self.__config_helper.get_configs(self.__config_name, "available_card")
        card_uids = [card[0] for card in available_card.values()]
        _id = str(_id)
        if isinstance(available_card, dict) and uid not in card_uids:
            available_card[_id] = [uid, key]
            self.__available_card = available_card
            return self.__config_helper.set_configs(self.__config_name, "available_card", available_card)
        return False

    def remove_available_card(self, _id):
        available_card = self.__config_helper.get_configs(self.__config_name, "available_card")
        _id = str(_id)
        if isinstance(available_card, dict) and available_card.get(_id):
            card_info = available_card.pop(_id)
            self.__available_card = available_card
            if self.__config_helper.set_configs(self.__config_name, "available_card", available_card):
                return card_info
        return False

    def remove_all_available_card(self):
        return self.__config_helper.set_configs(self.__config_name, "available_card", {})

    def change_key(self, old_key_B, new_key_A, new_key_B, area):
        card_uid = self.__check_card()
        if card_uid:
            block_start = (area - 1) * 4
            if self.__auth_B(block_start + 3, old_key_B, card_uid):
                result = self.__write_config(block_start + 3, new_key_A, new_key_B)
                if result:
                    print("mfrc522: change key success!!")
                    return True
                else:
                    print("mfrc522: change key failed!!")
            else:
                print("mfrc522: auth failed!!")
        return False

    def auto_read(self, area, block, key_A=None):
        card_uid = self.__check_card()
        if card_uid:
            card_uid_str = binascii.hexlify(bytes(card_uid))
            card_uid_str = card_uid_str.decode()
            print(f"card uid: {card_uid_str}")
            if not key_A:
                for uid, key in self.__available_card.values():
                    if uid == card_uid_str:
                        key = key.encode()
                        key = list(binascii.unhexlify(key))
                        key_A = key
            if key_A:
                block_start = (area - 1) * 4
                if self.__auth_A(block_start + 3, key_A, card_uid):
                    return self.__read(block_start + block)
                else:
                    print("mfrc522: auth failed!!")
        return False

    def auto_write(self, key_A, area, block, data):
        card_uid = self.__check_card()
        if card_uid:
            block_start = (area - 1) * 4
            if self.__auth_A(block_start + 3, key_A, card_uid):
                return self.__write(block_start + block, data)
            else:
                print("mfrc522: auth failed!!")
        return False
