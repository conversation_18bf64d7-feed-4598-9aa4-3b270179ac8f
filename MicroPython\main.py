from machine import Pin, freq
freq(240000000)
from core.sleep.sleep import SleepManager
from operation.index import Operation
from process.main import fpm383_read, gtx314l_read
from utils.time_util import get_tick, sleep
freq(160000000)


operation = Operation.get_instance()
# Power
power = Pin(12, Pin.OUT)
power.on()
# LED
led = Pin(13, Pin.OUT)
led.on()
sleep(100)
operation.fpm383.open_led_base()


def before_sleep_callback():
    led.off()
    operation.fpm383.close_led()
    destory_mqtt()
    wlan.disconnect()
    # 最多尝试 3 次取消命令，防止取消失败导致睡眠失败的可能性
    for _ in range(3):
        if operation.fpm383.cancel():
            break
    operation.fpm383.sleep()
    power.off()
    start_ulp()


def wake_callback():
    wlan.connect()
    power.on()
    sleep(100)
    led.on()
    operation.fpm383.open_led_base()


sleep_manager = SleepManager(
    timeout=4000,
    before_sleep_callback=before_sleep_callback,
    wake_callback=wake_callback,
    is_deepsleep=True
)


def gtx314l_read_process():
    for _ in range(50):
        if gtx314l_read():
            sleep_manager.report()


def base_process():
    if fpm383_read(callback=gtx314l_read_process):
        sleep_manager.report()

print(get_tick("test"))

for _ in range(3):
    base_process()


from core.network.wlan import WLAN
from process.blue import init as init_blue
from process.mqtt import destroy as destory_mqtt, report as report_mqtt, request_register_datas, start as start_mqtt
from ulp import start as start_ulp


wlan = WLAN.get_instance()
wlan.connect()


def open_bluetooth():
    sleep_manager.set_temporary_timeout(60000)
    init_blue()


operation.gtx314l.add_listener("*1234*", open_bluetooth)


while True:
    sleep_manager.run()
    base_process()
    if start_mqtt():
        sleep_manager.report()
    report_mqtt()
    request_register_datas()
