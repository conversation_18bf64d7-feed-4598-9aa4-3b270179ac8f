@echo off

chcp 65001

set /p SERIAL_PORT_NUM=请输入端口号数字（例如输入 3 表示 COM3):
set SERIAL_PORT=COM%SERIAL_PORT_NUM%

echo 开始烧写固件，请确保设备处于下载模式并手动复位，按任意键继续...
pause
esptool --port %SERIAL_PORT% erase_flash
if not "%errorlevel%" == "0" (goto FAILED)
esptool --port %SERIAL_PORT% --chip esp32 write_flash -z 0x1000 ./firmware.bin
if not "%errorlevel%" == "0" (goto FAILED)
echo 固件烧写完成，准备写入基础代码及配置文件，请确保设备已退出下载模式并手动复位，按任意键继续...
pause
echo 开始写入基础代码及配置文件，60 秒内如果没有相应，可能是设备未退出下载模式或未复位，请关闭此窗口然后重新烧写
ampy --port %SERIAL_PORT% put ./config
if not "%errorlevel%" == "0" (goto FAILED)
ampy --port %SERIAL_PORT% put ./process
if not "%errorlevel%" == "0" (goto FAILED)
ampy --port %SERIAL_PORT% put ./main.py
if not "%errorlevel%" == "0" (goto FAILED)
echo 基础代码及配置文件写入成功，请手动复位以运行程序
pause
exit

:FAILED
echo 烧写失败，请检查 (1) 端口号 (%SERIAL_PORT%) 是否错误，端口是否被其它程序占用 (2) 如果烧写过程中出现失败，请检查连接是否稳定 (3) 待写入文件是否缺失 (4) 烧写固件时需要处于下载模式并手动复位 (5) 写入基础代码及配置文件时需要退出下载模式并手动复位
pause
