#include "../../include/databases/fpm383_database.h"

// public (FPM383Record)

FPM383Record::FPM383Record() : used(false), uid(0) {}

// public (FPM383Database)

FPM383Database &FPM383Database::getInstance()
{
  static FPM383Database FPM383DB;
  return FPM383DB;
}

bool FPM383Database::add(int number, int uid)
{
  if (number < 1 || number > MAX_RECORDS)
  {
    print_log("fpm383", "add", "invalid number!!");
    return false;
  }
  if (records[number].used)
  {
    print_log("fpm383", "add", "number already used!!");
    return false;
  }
  records[number].used = true;
  records[number].uid = uid;
  return true;
}

bool FPM383Database::remove(int number)
{
  if (number < 1 || number > MAX_RECORDS)
  {
    print_log("fpm383", "remove", "invalid number!!");
    return false;
  }
  if (!records[number].used)
  {
    print_log("fpm383", "remove", "number not used!!");
    return false;
  }
  records[number].used = false;
  records[number].uid = 0;
  return true;
}

void FPM383Database::clear()
{
  for (int t = 1; t <= MAX_RECORDS; t++)
  {
    records[t].used = false;
    records[t].uid = 0;
  }
}

int FPM383Database::getNext()
{
  for (int t = 1; t <= MAX_RECORDS; t++)
  {
    if (!records[t].used)
    {
      return t;
    }
  }
  return 0;
}

int FPM383Database::getNumberByUid(int uid)
{
  for (int t = 1; t <= MAX_RECORDS; t++)
  {
    if (records[t].used && records[t].uid == uid)
    {
      return t;
    }
  }
  return 0;
}

int FPM383Database::getUidByNumber(int number)
{
  if (number <= MAX_RECORDS)
  {
    return records[number].uid;
  }
  return 0;
}

void FPM383Database::load()
{
  FileManager fileManager(FILE_NAME_FPM383, FILE_DEFAULT_FPM383);
  String record_string;
  if (fileManager.readString(record_string, fileManager.getSize()))
  {
    loadJson(record_string);
  }
}

void FPM383Database::store()
{
  FileManager fileManager(FILE_NAME_FPM383, FILE_DEFAULT_FPM383);
  String record_string;
  toJson(record_string);
  fileManager.write(record_string.c_str());
}

// private (FPM383Database)

FPM383Database::FPM383Database()
{
  load();
}

void FPM383Database::loadJson(const String &json_string)
{
  size_t size = get_json_size(MAX_RECORDS * 3, 1, MAX_RECORDS * 60);
  DynamicJsonDocument doc(size);
  DeserializationError error = deserializeJson(doc, json_string);
  if (error)
  {
    print_log("fpm383", "loadJson", "deserialization error!!");
    return;
  }
  JsonArray recordsArray = doc.as<JsonArray>();
  for (JsonObject recordJson : recordsArray)
  {
    if (recordJson["num"].isNull() || recordJson["used"].isNull() || recordJson["uid"].isNull())
    {
      print_log("fpm383", "loadJson", "error record format!!");
    }
    else
    {
      int number = recordJson["num"];
      bool used = recordJson["used"];
      int uid = recordJson["uid"];
      if (used)
      {
        add(number, uid);
      }
    }
  }
}

void FPM383Database::toJson(String &json_string) const
{
  size_t size = get_json_size(MAX_RECORDS * 3, 1, MAX_RECORDS * 60);
  DynamicJsonDocument doc(size);
  JsonArray recordsArray = doc.to<JsonArray>();
  for (int t = 1; t <= MAX_RECORDS; t++)
  {
    JsonObject recordJson = recordsArray.createNestedObject();
    recordJson["num"] = t;
    recordJson["used"] = records[t].used;
    recordJson["uid"] = records[t].uid;
  }
  serializeJson(doc, json_string);
}
