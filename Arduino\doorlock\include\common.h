#ifndef HW_S_COMMON_H
#define HW_S_COMMON_H

#include <string>
#include <vector>
#include <map>
#include <algorithm>
#include <functional>
#include <mbedtls/sha256.h>
#include <Arduino.h>

// 芯片类型：ESP32
// #define CORE_ESP32
// 芯片类型：ESP32-C3
#define CORE_ESP32_C3

// 调试模式（生产环境请注释）
#define DEBUG_MODE

// 蓝牙信息状态码：打招呼
const int BLUETOOTH_MSG_CODE_HELLO = 1;
// 蓝牙信息状态码：格式错误
const int BLUETOOTH_MSG_CODE_ERROR_FORMAT = 101;
// 蓝牙信息状态码：数据包大小超出限制
const int BLUETOOTH_MSG_CODE_ERROR_EXCEED_SIZE = 102;
// 蓝牙信息状态码：没有权限
const int BLUETOOTH_MSG_CODE_ERROR_AUTH = 111;
// 蓝牙信息状态码：权限等级错误
const int BLUETOOTH_MSG_CODE_ERROR_AUTH_LEVEL = 112;
// 蓝牙信息状态码：任务处理错误
const int BLUETOOTH_MSG_CODE_ERROR_STATUS = 121;
// 蓝牙信息状态码：任务处理成功
const int BLUETOOTH_MSG_CODE_SUCCESS_STATUS = 221;

// 操作记录类型：按键
const int OPERATION_TYPE_KEYPAD = 1;
// 操作记录类型：指纹
const int OPERATION_TYPE_FPM383 = 2;
// 操作记录类型：蓝牙
const int OPERATION_TYPE_BLUETOOTH = 3;
// 操作记录类型：MQTT
const int OPERATION_TYPE_MQTT = 4;

// 设备模式：每次随机生成超级管理员密码 + 不可修改管理员密码
const int DEVICE_MODE_0 = 0;
// 设备模式：固定逻辑生成超级管理员密码 + 可修改管理员密码
const int DEVICE_MODE_1 = 1;
// 设备模式：初次随机生成超级管理员密码 + 可修改管理员密码
const int DEVICE_MODE_2 = 2;

// 蓝牙数据包大小
const uint16_t BLUETOOTH_MSG_SIZE = 20;

// 基础串口波特率
const unsigned long SERIAL_BASE_BAUDRATE = 115200;
// 语音串口波特率
const unsigned long SERIAL_VOICE_BAUDRATE = 9600;
// 指纹串口波特率
const unsigned long SERIAL_FPM383_BAUDRATE = 57600;
// 语音串口配置
const uint32_t SERIAL_VOICE_CONFIG = SERIAL_8N1;
// 指纹串口配置
const uint32_t SERIAL_FPM383_CONFIG = SERIAL_8N2;
// 基础串口 RX
const uint8_t SERIAL_BASE_RX = 20;
// 基础串口 TX
const uint8_t SERIAL_BASE_TX = 21;
// 语音串口 RX
const uint8_t SERIAL_VOICE_RX = 4;
// 语音串口 TX
const uint8_t SERIAL_VOICE_TX = 16;
// 指纹串口 RX
const uint8_t SERIAL_FPM383_RX = 5;
// 指纹串口 TX
const uint8_t SERIAL_FPM383_TX = 18;
// 按键 I2C SDA
const int WIRE_KEYPAD_SDA = 21;
// 按键 I2C SCL
const int WIRE_KEYPAD_SCL = 22;
// 按键 I2C 地址
const int WIRE_KEYPAD_ADDRESS = 0x58;
// 按键灵敏度
const int KEYPAD_SENSITIVITY = 0x03;
// 按键表
const char KEYPAD_TABLE[12] = {'3', '2', '1', '6', '#', '9', '5', '4', '8', '0', '7', '*'};
// 指纹唤醒引脚
const gpio_num_t PIN_WAKEUP_FPM383 = (gpio_num_t)27;
// 按键唤醒引脚
const gpio_num_t PIN_WAKEUP_KEYPAD = (gpio_num_t)2;
// 按钮唤醒引脚
const uint8_t PIN_WAKEUP_BUTTON = 1;
// 电源引脚
const uint8_t PIN_POWER = 12;
// LED 引脚（蓝牙连接）
const uint8_t PIN_LED_BLUETOOTH = 6;
// LED 引脚（低电量报警）
const uint8_t PIN_LED_LOW_BATTERY = 7;
// 电机引脚 0
const uint8_t PIN_MOTOR_0 = 4;
// 电机引脚 1
const uint8_t PIN_MOTOR_1 = 5;
// 电量引脚
const uint8_t PIN_BATTERY = 3;

// 电池满电模拟量
const int BATTERY_FULL_VALUE = 2800;
// 电池空电模拟量
const int BATTERY_EMPTY_VALUE = 2500;    
// 低电量报警阈值
const int BATTERY_LOW_THRESHOLD = 25;

// 一般睡眠超时时间
const unsigned long SLEEP_TIMEOUT_NORMAL = 10000;
// 蓝牙启用睡眠超时时间（短）
const unsigned long SLEEP_TIMEOUT_BLUETOOTH_SHORT = 30000;
// 蓝牙启用睡眠超时时间（长）
const unsigned long SLEEP_TIMEOUT_BLUETOOTH_LONG = 60000;
// 电机转动默认时长
const unsigned long MOTOR_WORK_DEFAULT_TIME = 1000;
// 电机转动最大时长
const unsigned long MOTOR_WORK_MAX_TIME = 3000;
// 开关锁默认时间间隔
const unsigned long UNLOCK_DEFAULT_INTERVAL = 4000;
// 开关锁最大时间间隔
const unsigned long UNLOCK_MAX_INTERVAL = 10000;
// MQTT 报告时间间隔
const unsigned long MQTT_REPORT_INTERVAL = 60000;
// 开始处理 MQTT 信息时间
const unsigned long MQTT_START_PROCESS_TIME = SLEEP_TIMEOUT_NORMAL / 2;
// 蓝牙包发送时间间隔
const unsigned long BLUETOOTH_PACKET_SEND_INTERVAL = 50;

// 文件名称和默认值 Counter
const char FILE_NAME_COUNTER[] = "/counter.json";
const char FILE_DEFAULT_COUNTER[] = "{}";
// 文件名称和默认值 Keypad
const char FILE_NAME_KEYPAD[] = "/keypad.json";
const char FILE_DEFAULT_KEYPAD[] = "[]";
// 文件名称和默认值 FPM383
const char FILE_NAME_FPM383[] = "/fpm383.json";
const char FILE_DEFAULT_FPM383[] = "[]";
// 文件名称和默认值 Operation
const char FILE_NAME_OPERATION[] = "/operation.json";
const char FILE_DEFAULT_OPERATION[] = "[]";
// 文件名称和默认值 Config
const char FILE_NAME_CONFIG[] = "/config.json";
const char FILE_DEFAULT_CONFIG[] = "[]";
// 文件名称和默认值 BluetoothNote
const char FILE_NAME_BLUETOOTH_NOTE[] = "/bluetooth_note.json";
const char FILE_DEFAULT_BLUETOOTH_NOTE[] = "";

// 公用 MQTT 主题
const char MQTT_TOPIC_PUBLIC[] = "lock/data";
// MQTT 服务器端口
const unsigned short mqtt_port = 1883;

// 指纹数据包数量（请勿随意修改）
const int FPM383_PACK_NUM = 18;

// 语音播放指令
const char VOICE_PLAY[] = {0xAA, 0x02, 0x00, 0xAC};
// 语音音量设置 (15 / 30)
const char VOICE_SET_VOLUMN[] = {0xAA, 0x13, 0x01, 0x0F, 0xCD};
// 语音按键音指令
const char VOICE_TOUCH[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x54, 0x4f, 0x55, 0x43, 0x48, 0x2a, 0x3f, 0x3f, 0x3f, 0x41};
// 语音开锁指令
const char VOICE_UNLOCK[] = {0xAA, 0x08, 0x13, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x55, 0x4e, 0x4c, 0x4f, 0x43, 0x4b, 0x2a, 0x3f, 0x3f, 0x3f, 0x8B};
// 语言关锁指令
const char VOICE_LOCK[] = {0xAA, 0x08, 0x11, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x4c, 0x4f, 0x43, 0x4b, 0x2a, 0x3f, 0x3f, 0x3f, 0xE6};
// 语言验证失败指令
const char VOICE_ERROR[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x2a, 0x3f, 0x3f, 0x3f, 0x48};
// 语音更新中指令
const char VOICE_UPDATING[] = {0xAA, 0x08, 0x13, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x55, 0x50, 0x44, 0x49, 0x4e, 0x47, 0x2a, 0x3f, 0x3f, 0x3f, 0x86};
// 语音更新完成指令
const char VOICE_UPDATED[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x55, 0x50, 0x44, 0x4f, 0x4b, 0x2a, 0x3f, 0x3f, 0x3f, 0x41};
// 语音更新失败指令
const char VOICE_UPDATE_ERROR[] = {0xAA, 0x08, 0x13, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x55, 0x50, 0x44, 0x45, 0x52, 0x52, 0x2a, 0x3f, 0x3f, 0x3f, 0x91};
// 语音蓝牙开启
const char VOICE_BLUETOOTH_OPEN[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x42, 0x4c, 0x45, 0x4f, 0x50, 0x2a, 0x3f, 0x3f, 0x3f, 0x30};
// 语音指纹录入开始
const char VOICE_FPM383_START[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x46, 0x50, 0x4d, 0x53, 0x54, 0x2a, 0x3f, 0x3f, 0x3f, 0x48};
// 语音指纹再次录入
const char VOICE_FPM383_AGAIN[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x46, 0x50, 0x4d, 0x41, 0x47, 0x2a, 0x3f, 0x3f, 0x3f, 0x29};
// 语音指纹录入成功
const char VOICE_FPM383_OK[] = {0xAA, 0x08, 0x12, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x46, 0x50, 0x4d, 0x4f, 0x4b, 0x2a, 0x3f, 0x3f, 0x3f, 0x3B};
// 语音指纹录入失败
const char VOICE_FPM383_ERROR[] = {0xAA, 0x08, 0x13, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x46, 0x50, 0x4d, 0x45, 0x52, 0x52, 0x2a, 0x3f, 0x3f, 0x3f, 0x8B};
// 语音低电量
const char VOICE_LOW_BATTERY[] = {0xAA, 0x08, 0x13, 0x02, 0x2f, 0x53, 0x4f, 0x55, 0x4e, 0x44, 0x2a, 0x2f, 0x4c, 0x4f, 0x57, 0x42, 0x41, 0x54, 0x2a, 0x3f, 0x3f, 0x3f, 0x88};

// 指纹数据包头 v1
const char FPM383_HEADER_V1[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF};
// 指纹数据包头 v2
const char FPM383_HEADER_V2[] = {0xF1, 0x1F, 0xE2, 0x2E, 0xB6, 0x6B, 0xA8, 0x8A};
// 指纹触摸检测指令
const char FPM383_IS_PRESS[] = {0xF1, 0x1F, 0xE2, 0x2E, 0xB6, 0x6B, 0xA8, 0x8A, 0x00, 0x07, 0x86, 0x00, 0x00, 0x00, 0x00, 0x01, 0x35, 0xCA};
// 指纹验证命令
const char FPM383_VALIDATE[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x08, 0x32, 0x50, 0xFF, 0xFF, 0x00, 0x03, 0x02, 0x8C};
// 指纹取消指令
const char FPM383_CANCEL[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x30, 0x00, 0x34};
// 指纹清空指令
const char FPM383_CLEAR[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x0D, 0x00, 0x11};
// 指纹下载命令
const char FPM383_DOWNLOAD[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x04, 0x09, 0x02, 0x00, 0x10};
// 指纹睡眠指令
const char FPM383_SLEEP[] = {0xEF, 0x01, 0xFF, 0xFF, 0xFF, 0xFF, 0x01, 0x00, 0x03, 0x33, 0x00, 0x37};

// OTA 升级页面
const char OTA_UPLOAD_PAGE[] =
    "<script src='https://ajax.googleapis.com/ajax/libs/jquery/3.2.1/jquery.min.js'></script>"
    "<form method='POST' action='#' enctype='multipart/form-data' id='upload_form'>"
    "<input type='file' name='update'>"
    "<input type='submit' value='Update'>"
    "</form>"
    "<div id='prg'>progress: 0%</div>"
    "<script>"
    "$('form').submit(function(e){"
    "e.preventDefault();"
    "var form = $('#upload_form')[0];"
    "var data = new FormData(form);"
    " $.ajax({"
    "url: '/update',"
    "type: 'POST',"
    "data: data,"
    "contentType: false,"
    "processData: false,"
    "xhr: function() {"
    "var xhr = new window.XMLHttpRequest();"
    "xhr.upload.addEventListener('progress', function(evt) {"
    "if (evt.lengthComputable) {"
    "var per = evt.loaded / evt.total;"
    "$('#prg').html('progress: ' + Math.round(per*100) + '%');"
    "}"
    "}, false);"
    "return xhr;"
    "},"
    "success:function(d, s) {"
    "console.log('success!')"
    "},"
    "error: function (a, b, c) {"
    "}"
    "});"
    "});"
    "</script>";

#endif
