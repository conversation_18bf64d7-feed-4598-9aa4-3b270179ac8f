import json
import binascii
from machine import reset
import bluetooth
from core.config.config import Config<PERSON><PERSON><PERSON>
from core.blue.bluetooth import BLESimplePeripheral
import sys
from utils.base_util import get_unique_id
from utils.file_util import read_file, write_file

config_helper = ConfigHelper.get_instance()
ble_instance = None
ble_received_config_modify = None
keys = [
    "wlan/wlan_name",
    "wlan/wlan_password",
    "mqtthelper/server",
    "mqtthelper/user",
    "mqtthelper/password"
]


def receive(message):
    try:
        message = message.decode()
        data = json.loads(message)
        print(data)
        for key in keys:
            value = data.get(key)
            if value and isinstance(value, str):
                c_name, c_key = key.split("/")
                # config_helper.set_configs(c_name, c_key, value)

                # prevent stack overflow
                path = f"/config/{c_name}.json"
                content = read_file(path)
                try:
                    content = json.loads(content)
                except Exception:
                    content = {}
                content[c_key] = value
                content = json.dumps(content)
                write_file(path, content)

                # ble_instance.send(json.dumps({
                #     "key": key,
                #     "status": True
                # }))
        reset()
    except Exception as err:
        sys.print_exception(err)


def destroy():
    global ble_instance
    if ble_instance:
        ble_instance.disconnect()
        ble_instance = None


def init():
    global ble_instance
    if not ble_instance:
        blue = bluetooth.BLE()
        ble_instance = BLESimplePeripheral(blue, get_unique_id(sub_name="blue"))
        _, mac_address = blue.config("mac")
        mac_address = binascii.hexlify(mac_address)
        mac_address = mac_address.decode()
        print(mac_address)
    ble_instance.on_write(receive)
    if ble_instance.is_connected():
        ble_instance.notify("Hello!!")
