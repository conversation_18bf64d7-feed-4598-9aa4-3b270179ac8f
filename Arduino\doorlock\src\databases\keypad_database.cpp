#include "../../include/databases/keypad_database.h"

// public (KeypadRecord)

KeypadRecord::KeypadRecord(int id, const String &pwd) : userId(id), password(pwd) {}

// public (KeypadDatabase)

KeypadDatabase &KeypadDatabase::getInstance()
{
  static KeypadDatabase keypadDB;
  return keypadDB;
}

bool KeypadDatabase::add(int userId, const String &password)
{
  for (int t = 0; t < numRecords; t++)
  {
    if (records[t].userId == userId && records[t].password == password)
    {
      print_log("keypad", "add", "record already exists!!");
      return false;
    }
  }
  if (numRecords < MAX_RECORDS)
  {
    records[numRecords].userId = userId;
    records[numRecords].password = password;
    numRecords++;
    return true;
  }
  else
  {
    print_log("keypad", "add", "number of records exceeds the maximum limit!!");
    return false;
  }
}

bool KeypadDatabase::remove(int userId, const String &password)
{
  for (int t0 = 0; t0 < numRecords; t0++)
  {
    if (records[t0].userId == userId && records[t0].password == password)
    {
      for (int t1 = t0; t1 < numRecords - 1; t1++)
      {
        records[t1].userId = records[t1 + 1].userId;
        records[t1].password = records[t1 + 1].password;
      }
      numRecords--;
      return true;
    }
  }
  print_log("keypad", "remove", "no record found!!");
  return false;
}

void KeypadDatabase::clear()
{
  for (int t = 0; t < numRecords; t++)
  {
    records[t].userId = 0;
    records[t].password = "";
  }
  numRecords = 0;
}

void KeypadDatabase::findByPassword(const String &password, std::vector<KeypadRecord> &matching_record) const
{
  for (int t = 0; t < numRecords; t++)
  {
    if (records[t].password == password)
    {
      matching_record.push_back(records[t]);
    }
  }
}

void KeypadDatabase::load()
{
  FileManager fileManager(FILE_NAME_KEYPAD, FILE_DEFAULT_KEYPAD);
  String record_string;
  if (fileManager.readString(record_string, fileManager.getSize()))
  {
    loadJson(record_string);
  }
}

void KeypadDatabase::store()
{
  FileManager fileManager(FILE_NAME_KEYPAD, FILE_DEFAULT_KEYPAD);
  String record_string;
  toJson(record_string);
  fileManager.write(record_string.c_str());
}

// private (KeypadDatabase)

KeypadDatabase::KeypadDatabase() : numRecords(0)
{
  load();
}

void KeypadDatabase::loadJson(const String &json_string)
{
  size_t size = get_json_size(200, 1, json_string.length());
  DynamicJsonDocument doc(size);
  DeserializationError error = deserializeJson(doc, json_string);
  if (error)
  {
    print_log("keypad", "loadJson", "deserialization error!!");
    return;
  }
  JsonArray recordsArray = doc.as<JsonArray>();
  for (JsonObject recordJson : recordsArray)
  {
    if (recordJson["uid"].isNull() || recordJson["pwd"].isNull())
    {
      print_log("keypad", "loadJson", "error record format!!");
    }
    else
    {
      int userId = recordJson["uid"];
      String password = recordJson["pwd"];
      add(userId, password);
    }
  }
}

void KeypadDatabase::toJson(String &json_string) const
{
  size_t size = get_json_size(numRecords * 2, 1, numRecords * 40);
  DynamicJsonDocument doc(size);
  JsonArray recordsArray = doc.to<JsonArray>();
  for (int t = 0; t < numRecords; t++)
  {
    JsonObject recordJson = recordsArray.createNestedObject();
    recordJson["uid"] = records[t].userId;
    recordJson["pwd"] = records[t].password;
  }
  serializeJson(doc, json_string);
}
