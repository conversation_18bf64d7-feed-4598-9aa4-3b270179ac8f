import sys
import json
import webrepl
from machine import ADC, Pin
from core.config.config import Config<PERSON>elper
from core.network.wlan import WLAN
from core.mqtt.mqtt_helper import MQTTHelper
from core.mqtt.mqtt_message import MQTTMessage
from operation.index import Operation
from utils.time_util import get_tick
from utils.base_util import get_unique_id

config_helper = ConfigHelper.get_instance()
operation = Operation.get_instance()
wlan = WLAN.get_instance()
# mqtt 实例
mqtt_instance = None
# mqtt 连接状态
mqtt_connected = False
# 公用 mqtt 主题
public_topic = "lock/data"
# 设备专有 mqtt 主题
self_topic = f"lock/{get_unique_id(sub_name='mqtt')}"
# 设备标识
device_entity = get_unique_id(sub_name="entity")
# 设备名称
device_name = get_unique_id(name="智能锁")
# fingerprint 数据
fingerprint_datas = []
# ADC 电量
adc = ADC(Pin(33))


def device_operation_callback(fx_id, status):
    if mqtt_instance:
        data = {
            "lock_received": {
                "fx_id": fx_id,
                "state": "ok" if status else "err",
            }
        }
        data = json.dumps(data)
        mqtt_instance.publish(public_topic, data)
    else:
        print("mqtt: no instance found!!")


def password_add_callback(data):
    _id = data.get("id")
    fx_id = data.get("fx_id")
    numbers = data.get("pwd_num")
    if operation.gtx314l.create_password(_id=_id, numbers=numbers):
        device_operation_callback(fx_id, True)
        return True
    device_operation_callback(fx_id, False)
    return False


def password_del_callback(data):
    _id = data.get("id")
    fx_id = data.get("fx_id")
    if operation.gtx314l.remove_password(_id=_id):
        device_operation_callback(fx_id, True)
        return True
    device_operation_callback(fx_id, False)
    return False


def ic_card_add_callback(data):
    _id = data.get("id")
    fx_id = data.get("fx_id")
    card_info = data.get("ic_num")
    try:
        card_info = json.loads(card_info)
        if isinstance(card_info, list) and len(card_info) == 2:
            if operation.mfrc522.create_available_card(_id=_id, uid=card_info[0], key=card_info[1]):
                device_operation_callback(fx_id, True)
                return True
        device_operation_callback(fx_id, False)
        return False
    except Exception as err:
        sys.print_exception(err)
        device_operation_callback(fx_id, False)
        return False


def ic_card_del_callback(data):
    _id = data.get("id")
    fx_id = data.get("fx_id")
    if operation.mfrc522.remove_available_card(_id=_id):
        device_operation_callback(fx_id, True)
        return True
    device_operation_callback(fx_id, False)
    return False


def fingerprint_add_callback(data):
    global fingerprint_datas
    ref_id = data.get("id")
    fx_id = data.get("fx_id")
    index = data.get("index")
    total = data.get("total")
    info = data.get("f_info")
    if index == 0:
        fingerprint_datas.clear()
    fingerprint_datas.append(info)
    if index >= total - 1:
        print("start download!!")
        operation.fpm383.cancel()
        _id = operation.fpm383.download(data="\n".join(fingerprint_datas))
        fingerprint_datas.clear()
        print(f"next id: {_id}")
        if _id and _id > 0:
            if operation.fpm383.create_reference_id(ref_id=ref_id, _id=_id):
                device_operation_callback(fx_id, True)
                return True
    device_operation_callback(fx_id, False)
    return False


def fingerprint_del_callback(data):
    ref_id = data.get("id")
    fx_id = data.get("fx_id")
    _id = operation.fpm383.remove_reference_id(ref_id=ref_id)
    if operation.fpm383.delete_model(_id):
        device_operation_callback(fx_id, True)
        return True
    device_operation_callback(fx_id, False)
    return False


def self_topic_callback(message):
    try:
        print(f"[self] {message}")
        if message == "__stop__":
            sys.exit(0)
        message = json.loads(message)
        is_action = message.get("is_action")
        _type = message.get("type")
        action = message.get("action")
        data = message.get("data")
        if is_action == "start":
            operation.jq8900.sound_updating()
        elif is_action == "end":
            operation.jq8900.sound_updated()
        elif _type == "pwd":
            if action == "add":
                return password_add_callback(data)
            elif action == "del":
                return password_del_callback(data)
        elif _type == "ic":
            if action == "add":
                return ic_card_add_callback(data)
            elif action == "del":
                return ic_card_del_callback(data)
        elif _type == "fin":
            if action == "add":
                return fingerprint_add_callback(data)
            elif action == "del":
                return fingerprint_del_callback(data)
    except Exception as err:
        sys.print_exception(err)
        return False


def public_topic_callback(message):
    try:
        print(f"[public] {message}")
        return True
    except Exception as err:
        sys.print_exception(err)
        return False


def mqtt_callback(topic, message):
    try:
        global last_received_time
        global fingerprint_datas
        topic, message = topic.decode(), message.decode()
        if topic == public_topic:
            public_topic_callback(message)
        if topic == self_topic:
            self_topic_callback(message)
        get_tick("mqtt_callback", reset=True)
    except Exception as err:
        sys.print_exception(err)


def publish(message):
    if mqtt_instance:
        mqtt_instance.publish(self_topic, message)
    else:
        print("mqtt: no instance found!!")


def request_register_datas(force=False):
    if mqtt_instance:
        if get_tick("mqtt_request_register", reset=False) > 150000 or force:
            data = {
                "lock_action": {
                    "lock_topic": self_topic,
                    "msg": "ok"
                }
            }
            data = json.dumps(data)
            mqtt_instance.publish(public_topic, data)
            get_tick("mqtt_request_register", reset=True)
    else:
        print("mqtt: no instance found!!")


def report(force=False):
    if mqtt_instance:
        if get_tick("mqtt_report", reset=False) > 150000 or force:
            battery = adc.read()
            battery = max(min(battery, 2000), 1000)
            battery -= 1000
            battery /= 10
            ip_address = wlan.get_config()
            data = {
                "lock_info": {
                    "entity_id": device_entity,
                    "device_name": device_name,
                    "device_status": "on",
                    "electric_quantity": battery,
                    "lock_topic": self_topic,
                    "ip_address": ip_address
                }
            }
            data = json.dumps(data)
            mqtt_instance.publish(public_topic, data)
            get_tick("mqtt_report", reset=True)
    else:
        print("mqtt: no instance found!!")


def init():
    global mqtt_instance
    global mqtt_connected

    if wlan.get_config():
        if not mqtt_instance:
            mqtt_instance = MQTTHelper.get_instance(
                client_id="client01",
                callback=mqtt_callback
            )
            mqtt_connected = False
        if not mqtt_connected:
            mqtt_instance.connect(topic=[
                public_topic,
                self_topic
            ])
            report(force=True)
            request_register_datas(force=True)
            mqtt_connected = True
    else:
        print("mqtt: wlan not connected!!")


def destroy():
    global mqtt_connected

    if mqtt_instance:
        mqtt_connected = False
        mqtt_instance.disconnect()
    else:
        print("mqtt: no instance found!!")


def start():
    if mqtt_instance:
        if not mqtt_connected:
            init()
        mqtt_instance.receive()
        if get_tick("mqtt_callback", reset=False) < 1000:
            return True
    else:
        print("mqtt: no instance found, init mqtt...")
        init()
