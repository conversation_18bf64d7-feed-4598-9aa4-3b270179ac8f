#include "../../include/databases/counter.h"

// public

Counter &Counter::getInstance()
{
  static Counter counter;
  return counter;
}

void Counter::add(String item)
{
  if (counters.find(item) != counters.end())
  {
    counters[item]++;
  }
  else
  {
    counters[item] = 1;
  }
}

int Counter::getCount(String item)
{
  if (counters.find(item) != counters.end())
  {
    return counters[item];
  }
  else
  {
    return 0;
  }
}

void Counter::getAllCount(String &json_string) const
{
  toJson(json_string);
}

void Counter::remove(String item)
{
  counters.erase(item);
}

void Counter::removeAll()
{
  counters.clear();
}

void Counter::load()
{
  FileManager fileManager(FILE_NAME_COUNTER, FILE_DEFAULT_COUNTER);
  String record_string;
  if (fileManager.readString(record_string, fileManager.getSize()))
  {
    loadJson(record_string);
  }
}

void Counter::store()
{
  FileManager fileManager(FILE_NAME_COUNTER, FILE_DEFAULT_COUNTER);
  String record_string;
  toJson(record_string);
  fileManager.write(record_string.c_str());
}

// private

Counter::Counter()
{
  load();
}

void Counter::loadJson(const String &json_string)
{
  size_t counter_size = counters.size();
  size_t size = get_json_size(200, 1, json_string.length());
  DynamicJsonDocument doc(size);
  DeserializationError error = deserializeJson(doc, json_string);
  if (error)
  {
    print_log("keypad", "loadJson", "deserialization error!!");
    return;
  }
  removeAll();
  for (const auto &entry : doc.as<JsonObjectConst>())
  {
    String key(entry.key().c_str());
    int value = entry.value().as<int>();
    counters[key] = value;
  }
}

void Counter::toJson(String &json_string) const
{
  size_t counter_size = counters.size();
  size_t size = get_json_size(counter_size, 0, counter_size * 40);
  DynamicJsonDocument doc(size);
  for (const auto &entry : counters)
  {
    doc[entry.first] = entry.second;
  }
  serializeJson(doc, json_string);
}
