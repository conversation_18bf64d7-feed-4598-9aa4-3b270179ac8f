import binascii
from core.uart.uart import SimpleUART
from utils.hex_util import string_to_hex, to_hex_string


class JQ8900():
    def __init__(self, rx, tx, index):
        self.__uart = SimpleUART(rx=rx, tx=tx, baudrate=9600, index=index, stop=1)

    def make_command(self, body):
        header = "AA"
        command = f"{header}{body}"
        command_hex_value = binascii.unhexlify(command)
        command_hex_value = list(command_hex_value)
        command_hex_sum = sum(command_hex_value)
        checksum = to_hex_string(command_hex_sum & 0xFF, 2)
        return f"{command}{checksum}"

    def test_command(self, command, delay):
        data = self.__uart.send_and_receive_hex(command, delay=delay)
        return data

    def play(self):
        command = "AA0200AC"
        self.__uart.send_hex(command)

    def pause(self):
        command = "AA0300AD"
        self.__uart.send_hex(command)

    def stop(self):
        command = "AA0400AE"
        self.__uart.send_hex(command)

    def prev_music(self):
        command = "AA0500AF"
        self.__uart.send_hex(command)

    def next_music(self):
        command = "AA0600B0"
        self.__uart.send_hex(command)

    def select_index(self, index):
        command = self.make_command(f"0702{to_hex_string(index, 4)}")
        self.__uart.send_hex(command)

    def select_path(self, disk, path):
        full_path = f"{to_hex_string(disk, 2)}{string_to_hex(path, 'gbk')}"
        length = to_hex_string(len(full_path) // 2, 2)
        command = self.make_command(f"08{length}{full_path}")
        self.__uart.send_hex(command)

    def query_online_disk(self):
        command = "AA0900B3"
        data = self.__uart.send_and_receive_hex(command)
        if data:
            disk_value = data[-4:-2]
            disk_value = list(binascii.unhexlify(disk_value))[0]
            online_disk = []
            if disk_value & 0x01 > 0:
                online_disk.append("USB(0)")
            if disk_value & 0x02 > 0:
                online_disk.append("SD(1)")
            if disk_value & 0x04 > 0:
                online_disk.append("FLASH(2)")
            return online_disk

    def change_disk(self, disk):
        command = self.make_command(f"0B01{to_hex_string(disk, 2)}")
        self.__uart.send_hex(command)

    def set_volumn(self, volumn):
        command = self.make_command(f"1301{to_hex_string(volumn, 2)}")
        self.__uart.send_hex(command)

    def increase_volumn(self):
        command = "AA1400BE"
        self.__uart.send_hex(command)

    def decrease_volumn(self):
        command = "AA1500BF"
        self.__uart.send_hex(command)
