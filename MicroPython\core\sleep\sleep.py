import sys
import esp32
from machine import Pin, lightsleep, deepsleep
from utils.time_util import get_tick, sleep


class SleepManager():
    def __init__(self, timeout, before_sleep_callback=None, wake_callback=None, is_deepsleep=False):
        self.__timeout = timeout
        self.__temporary_timeout = self.__timeout
        self.__before_sleep_callback = before_sleep_callback
        self.__wake_callback = wake_callback
        self.__is_deepsleep = is_deepsleep
        self.__tick = 0
        self.__get_tick()

    def __get_tick(self):
        return get_tick("__sleep_manager", reset=True)

    def set_ext0_wakeup(self, wake_pin, wake_value):
        try:
            pin = Pin(wake_pin, Pin.IN)
            esp32.wake_on_ext0(pin, esp32.WAKEUP_ANY_HIGH if wake_value else esp32.WAKEUP_ALL_LOW)
        except Exception as err:
            sys.print_exception(err)

    def set_ext1_wakeup(self, wake_pins, wake_value):
        try:
            pins = []
            for pin in wake_pins:
                pins.append(Pin(pin, Pin.IN))
            esp32.wake_on_ext1(pins, esp32.WAKEUP_ANY_HIGH if wake_value else esp32.WAKEUP_ALL_LOW)
        except Exception as err:
            sys.print_exception(err)

    def set_temporary_timeout(self, timeout):
        self.__temporary_timeout = timeout

    def extend(self, time_ms):
        self.__temporary_timeout += time_ms

    def report(self):
        self.__tick = 0
        self.__get_tick()

    def run(self):
        self.__tick += self.__get_tick()
        if self.__tick > self.__temporary_timeout:
            self.__tick = 0
            self.__temporary_timeout = self.__timeout
            if self.__before_sleep_callback and callable(self.__before_sleep_callback):
                self.__before_sleep_callback()
            print("enter sleep mode!!")
            sleep(100)
            deepsleep() if self.__is_deepsleep else lightsleep()
            print("wake up!!")
            self.__get_tick()
            if self.__wake_callback and callable(self.__wake_callback):
                self.__wake_callback()
