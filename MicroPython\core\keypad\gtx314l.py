import sys
from machine import Pin, SoftI2C
from core.config.config import ConfigHelper
from utils.time_util import get_tick, sleep


class GTX314L():
    KEY_12_BASE = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "*", "0", "#"]
    KEY_12_REVERSE = ["#", "0", "*", "9", "8", "7", "6", "5", "4", "3", "2", "1"]

    def __init__(self, scl, sda, addr, max_pressed_key=20, delay=100, sensitivity=0x0F, keypad_layout=KEY_12_BASE):
        try:
            self.__max_pressed_key = max_pressed_key
            self.__delay = delay
            self.__sensitivity = sensitivity
            self.__keypad_layout = keypad_layout
            self.__pressed_key_list = []
            self.__listener_list = {}
            self.__last_pressed_status = False
            self.__i2c_addr = addr
            self.__i2c = SoftI2C(scl=Pin(scl), sda=Pin(sda), freq=100000)
            self.__passwords = {}
            self.__config_name = self.__class__.__name__.lower()
            self.__config_helper = ConfigHelper.get_instance()
            self.__init_config()
            self.__read_passwords()
            self.__init()
        except Exception as err:
            sys.print_exception(err)

    def __init_config(self, force_clear=False):
        self.__config_helper.create_config([self.__config_name])
        password = self.__config_helper.get_configs(self.__config_name, "password")
        status = True
        if not isinstance(password, dict) or force_clear:
            password = {}
            if self.__config_helper.set_configs(self.__config_name, "password", password):
                print("gtx314l: config item [password] is created!!")
            else:
                status = False
        return status

    def __read_passwords(self):
        password = self.__config_helper.get_configs(self.__config_name, "password")
        if isinstance(password, dict):
            self.__passwords = password
            return True
        return False

    def __init(self):
        try:
            self.__i2c.start()
            sleep(5)
            # set sensitivity
            for addr in range(0x20, 0x2E):
                self.__write([addr, self.__sensitivity])
            # enable all channel
            self.__write([0x04, 0xFF])
            self.__write([0x05, 0x3F])
            # level mode interrupt and single touch mode
            self.__write([0x10, 0x10])
            self.__i2c.stop()
            return True
        except Exception as err:
            sys.print_exception(err)
            return False

    def __read(self, mem_addr, size):
        try:
            return self.__i2c.readfrom_mem(self.__i2c_addr, mem_addr, size)
        except Exception as err:
            sys.print_exception(err)
            return False

    def __write(self, value):
        try:
            self.__i2c.writeto(self.__i2c_addr, bytearray(value))
            return True
        except Exception as err:
            sys.print_exception(err)
            return False

    def __get_pressed_key(self):
        try:
            self.__i2c.start()
            sleep(5)
            data = self.__read(0x02, 2)
            data = list(data)
            g1, g2 = data
            pressed_keys = []

            if (g1 & 0x01) >> 0:
                pressed_keys.append(self.__keypad_layout[0])
            if (g1 & 0x02) >> 1:
                pressed_keys.append(self.__keypad_layout[1])
            if (g1 & 0x04) >> 2:
                pressed_keys.append(self.__keypad_layout[2])
            if (g1 & 0x08) >> 3:
                pressed_keys.append(self.__keypad_layout[3])
            if (g1 & 0x10) >> 4:
                pressed_keys.append(self.__keypad_layout[4])
            if (g1 & 0x20) >> 5:
                pressed_keys.append(self.__keypad_layout[5])
            if (g1 & 0x40) >> 6:
                pressed_keys.append(self.__keypad_layout[6])
            if (g1 & 0x80) >> 7:
                pressed_keys.append(self.__keypad_layout[7])

            if (g2 & 0x01) >> 0:
                pressed_keys.append(self.__keypad_layout[8])
            if (g2 & 0x02) >> 1:
                pressed_keys.append(self.__keypad_layout[9])
            if (g2 & 0x04) >> 2:
                pressed_keys.append(self.__keypad_layout[10])
            if (g2 & 0x08) >> 3:
                pressed_keys.append(self.__keypad_layout[11])

            self.__i2c.stop()
            return pressed_keys
        except Exception as err:
            sys.print_exception(err)
            return False

    def __check_listener(self):
        try:
            numbers = "".join(self.__pressed_key_list)
            for listen_numbers, callback in self.__listener_list.items():
                if numbers[-len(listen_numbers):] == listen_numbers:
                    if callback and callable(callback):
                        callback()
                        return True
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def __check_passwords(self, confirm):
        try:
            numbers = "".join(self.__pressed_key_list)
            if numbers and numbers[-1] == confirm:
                for _id, password in self.__passwords.items():
                    if confirm and isinstance(confirm, str):
                        password += confirm
                    if numbers[-len(password):] == password:
                        return int(_id)
                return -1
            return False
        except Exception as err:
            sys.print_exception(err)
            return False

    def create_password(self, _id, numbers):
        password = self.__config_helper.get_configs(self.__config_name, "password")
        _id = str(_id)
        if isinstance(password, dict) and numbers not in password.values():
            password[_id] = numbers
            self.__passwords = password
            return self.__config_helper.set_configs(self.__config_name, "password", password)
        return False

    def remove_password(self, _id):
        password = self.__config_helper.get_configs(self.__config_name, "password")
        _id = str(_id)
        if isinstance(password, dict) and password.get(_id):
            numbers = password.pop(_id)
            self.__passwords = password
            if self.__config_helper.set_configs(self.__config_name, "password", password):
                return numbers
        return False

    def remove_all_password(self):
        return self.__config_helper.set_configs(self.__config_name, "password", {})

    def add_listener(self, numbers, callback):
        if isinstance(numbers, str) and callable(callback):
            self.__listener_list[numbers] = callback

    def is_press(self, max_key=1):
        pressed_key = self.__get_pressed_key()
        if pressed_key and len(pressed_key) <= max_key:
            return pressed_key
        return False

    def record_key(self, max_key=1, enable_only_not_press=True):
        pressed_key = self.is_press(max_key)
        if pressed_key:
            if get_tick(self.__config_name, reset=False) > self.__delay:
                if not enable_only_not_press or not self.__last_pressed_status:
                    self.__last_pressed_status = True
                    self.__pressed_key_list.extend(pressed_key)
                    if len(self.__pressed_key_list) > self.__max_pressed_key:
                        self.__pressed_key_list = self.__pressed_key_list[-self.__max_pressed_key:]
                    self.__check_listener()
                    _id = self.__check_passwords(confirm="#")
                    get_tick(self.__config_name, reset=True)
                    print("-------  current pressed key list  ---------")
                    print("".join(self.__pressed_key_list))
                    if _id:
                        return _id
                    else:
                        return True
        else:
            self.__last_pressed_status = False
        return False
