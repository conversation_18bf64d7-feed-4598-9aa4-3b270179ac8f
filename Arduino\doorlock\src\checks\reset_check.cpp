#include "../../include/checks/reset_check.h"

// 重启异常检测及处理
void check_reset_exception()
{
  esp_reset_reason_t reset_reason = esp_reset_reason();
  switch (reset_reason)
  {
  case ESP_RST_UNKNOWN:
    // 未知原因重启
    print_log("reset", "reason", "Reset reason can not be determined.");
    Counter::getInstance().add("reset/unknown");
    break;
  case ESP_RST_POWERON:
    // 上电重启
    print_log("reset", "reason", "Reset due to power-on event.");
    Counter::getInstance().add("reset/poweron");
    break;
  case ESP_RST_EXT:
    // 外部引脚重启（不适用于 ESP32）
    print_log("reset", "reason", "Reset by external pin.");
    Counter::getInstance().add("reset/external");
    break;
  case ESP_RST_SW:
    // 软件重启
    print_log("reset", "reason", "Software reset via esp_restart.");
    Counter::getInstance().add("reset/restart");
    break;
  case ESP_RST_PANIC:
    // 软件故障重启
    print_log("reset", "reason", "Software reset due to exception/panic.");
    Counter::getInstance().add("reset/panic");
    break;
  case ESP_RST_INT_WDT:
    // 中断看门狗定时器重启
    print_log("reset", "reason", "Reset due to interrupt watchdog.");
    Counter::getInstance().add("reset/intwdt");
    break;
  case ESP_RST_TASK_WDT:
    // 任务看门狗定时器重启
    print_log("reset", "reason", "Reset due to task watchdog.");
    Counter::getInstance().add("reset/taskwdt");
    break;
  case ESP_RST_WDT:
    // 其它看门狗定时器重启
    print_log("reset", "reason", "Reset due to other watchdogs.");
    Counter::getInstance().add("reset/otherwdt");
    break;
  case ESP_RST_DEEPSLEEP:
    // 深度睡眠重启
    print_log("reset", "reason", "Reset after exiting deep sleep mode.");
    Counter::getInstance().add("reset/deepsleep");
    break;
  case ESP_RST_BROWNOUT:
    // 电压不稳定重启
    print_log("reset", "reason", "Brownout reset.");
    Counter::getInstance().add("reset/brownout");
    break;
  case ESP_RST_SDIO:
    // SDIO 重启（SD 卡传输错误重启）
    print_log("reset", "reason", "Reset over SDIO.");
    Counter::getInstance().add("reset/sdio");
    break;
  default:
    // 未知原因
    print_log("reset", "reason", "Unknown or unsupported reset reason.");
    Counter::getInstance().add("reset/unknown");
    break;
  }
  Counter::getInstance().store();
}
