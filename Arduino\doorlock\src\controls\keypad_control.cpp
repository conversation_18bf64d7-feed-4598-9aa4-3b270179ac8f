#include "../../include/controls/keypad_control.h"

// 当前按下状态
bool keypad_is_press = false;
// 最近一次按键值
char last_keypad_value = 0;
// 当前按键按下记录
char current_keypad_records[21];

// 按键初始化
void keypad_init()
{
  // 初始化 I2C
  Wire.begin(WIRE_KEYPAD_SDA, WIRE_KEYPAD_SCL);

  // 设置按键灵敏度
  byte sensitivities[14];
  memset(sensitivities, KEYPAD_SENSITIVITY, 14);
  Wire.beginTransmission(WIRE_KEYPAD_ADDRESS);
  Wire.write(0x20);
  Wire.write(sensitivities, sizeof(sensitivities));
  Wire.endTransmission();

  // 设置 slide 模式
  byte slide_mode[1] = {0x01};
  Wire.beginTransmission(WIRE_KEYPAD_ADDRESS);
  Wire.write(0x19);
  Wire.write(slide_mode, sizeof(slide_mode));
  Wire.endTransmission();

  memset(current_keypad_records, 0, 21);
}

// 设置按键睡眠模式
void keypad_set_sleep_mode(int mode)
{
  byte sleep_mode[1] = {mode};
  Wire.beginTransmission(WIRE_KEYPAD_ADDRESS);
  Wire.write(0x0B);
  Wire.write(sleep_mode, sizeof(sleep_mode));
  Wire.endTransmission();
}

// 设置按键空闲时间
void keypad_set_idle_time(int time, int time_suffix)
{
  byte idle_time[2] = {time, time_suffix};
  Wire.beginTransmission(WIRE_KEYPAD_ADDRESS);
  Wire.write(0x14);
  Wire.write(idle_time, sizeof(idle_time));
  Wire.endTransmission();
}

// 按键读取
char keypad_read()
{
  Wire.beginTransmission(WIRE_KEYPAD_ADDRESS);
  Wire.write(0x02);
  Wire.endTransmission();
  Wire.requestFrom(WIRE_KEYPAD_ADDRESS, 2);
  byte buffer[2];
  Wire.readBytes(buffer, 2);

  if ((buffer[0] & 0x01) >> 0)
  {
    return KEYPAD_TABLE[0];
  }
  if ((buffer[0] & 0x02) >> 1)
  {
    return KEYPAD_TABLE[1];
  }
  if ((buffer[0] & 0x04) >> 2)
  {
    return KEYPAD_TABLE[2];
  }
  if ((buffer[0] & 0x08) >> 3)
  {
    return KEYPAD_TABLE[3];
  }
  if ((buffer[0] & 0x10) >> 4)
  {
    return KEYPAD_TABLE[4];
  }
  if ((buffer[0] & 0x20) >> 5)
  {
    return KEYPAD_TABLE[5];
  }
  if ((buffer[0] & 0x40) >> 6)
  {
    return KEYPAD_TABLE[6];
  }
  if ((buffer[0] & 0x80) >> 7)
  {
    return KEYPAD_TABLE[7];
  }

  if ((buffer[1] & 0x01) >> 0)
  {
    return KEYPAD_TABLE[8];
  }
  if ((buffer[1] & 0x02) >> 1)
  {
    return KEYPAD_TABLE[9];
  }
  if ((buffer[1] & 0x04) >> 2)
  {
    return KEYPAD_TABLE[10];
  }
  if ((buffer[1] & 0x08) >> 3)
  {
    return KEYPAD_TABLE[11];
  }

  return 0;
}

// 记录按键
void keypad_record(char key)
{
  int current_size = current_keypad_records[20];
  if (current_size >= 20)
  {
    for (int t = 0; t < 19; t++)
    {
      current_keypad_records[t] = current_keypad_records[t + 1];
    }
    current_keypad_records[19] = key;
  }
  else
  {
    current_keypad_records[current_size] = key;
  }
  current_size++;
  current_size = std::min(current_size, 20);
  current_keypad_records[20] = current_size;
}

// 按键验证流程
KeypadReadResult keypad_read_process()
{
  std::vector<KeypadRecord> matching_record;
  KeypadReadResult result;
  char key = keypad_read();
  if (key)
  {
    if (!keypad_is_press || last_keypad_value != key)
    {
      keypad_is_press = true;
      last_keypad_value = key;
      result.isPress = true;
      if (key != '#')
      {
        keypad_record(key);
      }
      for (int t = 10; t >= 6; t--)
      {
        matching_record.clear();
        int current_size = current_keypad_records[20];
        if (current_size >= t)
        {
          char record[t + 1];
          memset(record, 0, t + 1);
          strncpy(record, current_keypad_records + current_size - t, t);
          String record_str(record);
          KeypadDatabase::getInstance().findByPassword(record_str, matching_record);
          if (t == 10)
          {
            result.record10 = record_str;
          }
          if (matching_record.size() > 0)
          {
            KeypadRecord keypad_record = matching_record[0];
            result.isMatch = true;
            result.userId = keypad_record.userId;
            if (keypad_record.userId < 0)
            {
              memset(current_keypad_records, 0, 21);
            }
            break;
          }
        }
      }
      if (key == '#')
      {
        memset(current_keypad_records, 0, 21);
        result.isSharp = true;
      }
    }
  }
  else
  {
    keypad_is_press = false;
  }
  return result;
}
