#include "../../include/controls/bluetooth_control.h"

// 蓝牙连接状态
bool bluetooth_connect_status = false;
// 蓝牙连接 ID
uint16_t bluetooth_connect_id = 0xFF;

// 蓝牙等待状态
bool bluetooth_waiting_status = false;
// 当前接收数据包下标
int received_packets = 0;
// 预期接收数据包下标
int expected_packet_index = 0;
// 合并数据包信息
std::string complete_data = "";

// 获取蓝牙连接状态
bool bluetooth_is_connected()
{
  return bluetooth_connect_status;
}

// 获取蓝牙连接 ID
uint16_t get_bluetooth_connect_id()
{
  return bluetooth_connect_id;
}

// 获取蓝牙等待状态
bool get_bluetooth_waiting_status()
{
  return bluetooth_waiting_status;
}

// 获取合并数据包
void get_bluetooth_complete_data(std::string &data)
{
  data = complete_data;
}

// 重置蓝牙数据包
void reset_packet_data()
{
  received_packets = 0;
  expected_packet_index = 0;
  complete_data = "";
}

// 完成蓝牙数据处理
void finish_bluetooth_data_process()
{
  reset_packet_data();
  bluetooth_waiting_status = false;
}

// public (BluetoothServerCallbacks)

void BluetoothServerCallbacks::onConnect(BLEServer *pServer, esp_ble_gatts_cb_param_t *param)
{
  bluetooth_connect_status = true;
  bluetooth_connect_id = param->connect.conn_id;
  print_log("bluetooth", "connect", "device connected, id: " + String(param->connect.conn_id));
}

void BluetoothServerCallbacks::onDisconnect(BLEServer *pServer, esp_ble_gatts_cb_param_t *param)
{
  bluetooth_connect_status = false;
  bluetooth_connect_id = 0xFF;
  print_log("bluetooth", "connect", "device disconnected, id: " + String(param->connect.conn_id));
}

// public (BluetoothCharacteristicCallbacks)

void BluetoothCharacteristicCallbacks::onWrite(BLECharacteristic *pCharacteristic)
{
  if (!bluetooth_waiting_status)
  {
    std::string packet = pCharacteristic->getValue();
    if (packet.length() <= BLUETOOTH_MSG_SIZE)
    {
      if (packet.length() > 4)
      {
        int total_packets = strtol(packet.substr(0, 2).c_str(), nullptr, 16);
        int packet_index = strtol(packet.substr(2, 2).c_str(), nullptr, 16);
        if (packet_index == 0)
        {
          reset_packet_data();
          print_log("bluetooth", "callback", "reset packet data");
        }
        if (packet_index == expected_packet_index)
        {
          complete_data += packet.substr(4);
          received_packets++;
          expected_packet_index = received_packets;
          if (received_packets == total_packets)
          {
            bluetooth_waiting_status = true;
          }
        }
        else
        {
          reset_packet_data();
          print_log("bluetooth", "callback", "packet index not match, expected: " + String(expected_packet_index) + ", received: " + String(packet_index));
        }
      }
      else
      {
        reset_packet_data();
        print_log("bluetooth", "callback", "packet size too small, size: " + String(packet.length()));
      }
    }
    else
    {
      print_log("bluetooth", "callback", "packet size too large, size: " + String(packet.length()));
    }
  }
}
