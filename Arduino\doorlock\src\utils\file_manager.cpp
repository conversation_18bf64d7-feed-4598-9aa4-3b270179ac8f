#include "../../include/utils/file_manager.h"

// public

FileManager::FileManager(const char *fileName, const char *default_value) : fileName(fileName)
{
  if (!LittleFS.begin(true))
  {
    print_log("file", "init", "failed!!");
    return;
  }

  if (!exists())
  {
    write(default_value);
  }
}

size_t FileManager::getSize()
{
  File file = LittleFS.open(fileName, "r");

  if (file)
  {
    return file.size();
  }
  else
  {
    print_log("file", "getSize", "failed!!");
    return -1;
  }
}

bool FileManager::write(const char *content)
{
  File file = LittleFS.open(fileName, "w");

  if (file)
  {
    file.print(content);
    file.close();
    return true;
  }
  else
  {
    print_log("file", "write", "failed!!");
    return false;
  }
}

bool FileManager::read(char *content, size_t size)
{
  File file = LittleFS.open(fileName, "r");

  if (size >= 0 && file)
  {
    file.read((uint8_t *)content, size);
    file.close();
    return true;
  }
  else
  {
    print_log("file", "read", "failed!!");
    return false;
  }
}

bool FileManager::readString(String &content, size_t size)
{
  char buffer[size + 1];
  memset(buffer, 0, size + 1);
  bool status = read(buffer, size);
  content = String(buffer);
  return status;
}

// private

bool FileManager::exists()
{
  return LittleFS.exists(fileName);
}
