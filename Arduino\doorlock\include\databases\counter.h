#ifndef HW_S_COUNTER_H
#define HW_S_COUNTER_H

#include "../common.h"
#include "../utils/json.h"
#include "../utils/utils.h"
#include "../utils/file_manager.h"

// 计数器类
class Counter
{
public:
  Counter(const Counter &) = delete;
  Counter &operator=(const Counter &) = delete;

  static Counter &getInstance();

  void add(String item);

  int getCount(String item);

  void getAllCount(String &json_string) const;

  void remove(String item);

  void removeAll();

  void load();

  void store();

private:
  std::map<String, int> counters;

  Counter();

  void loadJson(const String &json_string);

  void toJson(String &json_string) const;
};

#endif
