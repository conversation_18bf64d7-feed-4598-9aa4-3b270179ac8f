#ifndef HW_S_SERIAL_H
#define HW_S_SERIAL_H

#include "../common.h"
#include <HardwareSerial.h>

// #define VoiceSerial Serial1
// #define FPM383Serial Serial2

// 初始化串口
void serial_init();

// 语音串口写入
void voice_write(const char *message, size_t size, uint32_t delay_time);

// 指纹串口读取
int fpm383_read(char *received_data, int size);

// 指纹串口写入
void fpm383_write(const char *message, size_t size, uint32_t delay_time);

#endif
