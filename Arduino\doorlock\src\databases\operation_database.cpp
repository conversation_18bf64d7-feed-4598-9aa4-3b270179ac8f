#include "../../include/databases/operation_database.h"

// public (OperationRecord)

OperationRecord::OperationRecord(int uid, int type, time_t timestamp) : uid(uid), type(type), timestamp(timestamp) {}

// public (OperationDatabase)

OperationDatabase &OperationDatabase::getInstance()
{
  static OperationDatabase operationDB;
  return operationDB;
}

bool OperationDatabase::add(int uid, int type, time_t timestamp)
{
  if (numRecords < MAX_RECORDS)
  {
    records[numRecords].uid = uid;
    records[numRecords].type = type;
    records[numRecords].timestamp = timestamp;
    numRecords++;
    return true;
  }
  else
  {
    print_log("operation", "add", "number of records exceeds the maximum limit!!");
    return false;
  }
}

bool OperationDatabase::remove(int index)
{
  if (index < numRecords)
  {
    for (int t = index; t < numRecords - 1; t++)
    {
      records[t].uid = records[t + 1].uid;
      records[t].type = records[t + 1].type;
      records[t].timestamp = records[t + 1].timestamp;
    }
    numRecords--;
    return true;
  }
  else
  {
    // print_log("operation", "remove", "no record found!!");
    return false;
  }
}

void OperationDatabase::clear()
{
  for (int t = 0; t < numRecords; t++)
  {
    records[t].uid = 0;
    records[t].type = 0;
    records[t].timestamp = 0;
  }
  numRecords = 0;
}

bool OperationDatabase::get(int index, OperationRecord &record)
{
  if (index < numRecords)
  {
    record = records[index];
    return true;
  }
  return false;
}

void OperationDatabase::load()
{
  FileManager fileManager(FILE_NAME_OPERATION, FILE_DEFAULT_OPERATION);
  String record_string;
  if (fileManager.readString(record_string, fileManager.getSize()))
  {
    loadJson(record_string);
  }
}

void OperationDatabase::store()
{
  FileManager fileManager(FILE_NAME_OPERATION, FILE_DEFAULT_OPERATION);
  String record_string;
  toJson(record_string);
  fileManager.write(record_string.c_str());
}

// private (OperationDatabase)

OperationDatabase::OperationDatabase() : numRecords(0)
{
  load();
}

void OperationDatabase::loadJson(const String &json_string)
{
  DynamicJsonDocument doc(1024);
  DeserializationError error = deserializeJson(doc, json_string);
  if (error)
  {
    print_log("operation", "loadJson", "deserialization error!!");
    return;
  }
  JsonArray recordsArray = doc.as<JsonArray>();
  for (JsonObject recordJson : recordsArray)
  {
    if (recordJson["uid"].isNull() || recordJson["type"].isNull() || recordJson["timestamp"].isNull())
    {
      print_log("operation", "loadJson", "error record format!!");
    }
    else
    {
      int uid = recordJson["uid"];
      int type = recordJson["type"];
      time_t timestamp = recordJson["timestamp"];
      add(uid, type, timestamp);
    }
  }
}

void OperationDatabase::toJson(String &json_string) const
{
  DynamicJsonDocument doc(1024);
  JsonArray recordsArray = doc.to<JsonArray>();
  for (int t = 0; t < numRecords; t++)
  {
    JsonObject recordJson = recordsArray.createNestedObject();
    recordJson["uid"] = records[t].uid;
    recordJson["type"] = records[t].type;
    recordJson["timestamp"] = records[t].timestamp;
  }
  serializeJson(doc, json_string);
}
