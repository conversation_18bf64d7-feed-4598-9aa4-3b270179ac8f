#include "../../include/utils/aes_ctr.h"

bool aes128_ctr_process(const uint8_t* input, size_t input_len, const uint8_t* key, const uint8_t* iv, uint8_t* output) {
    mbedtls_aes_context aes;
    mbedtls_aes_init(&aes);

    if (mbedtls_aes_setkey_enc(&aes, key, 128) != 0) {
        mbedtls_aes_free(&aes);
        return false;
    }

    uint8_t nonce_counter[BLOCK_SIZE];
    uint8_t stream_block[BLOCK_SIZE];
    size_t nc_off = 0;

    memcpy(nonce_counter, iv, BLOCK_SIZE);
    memset(stream_block, 0, BLOCK_SIZE);

    int result = mbedtls_aes_crypt_ctr(&aes, input_len, &nc_off, nonce_counter, stream_block, input, output);

    mbedtls_aes_free(&aes);
    return result == 0;
}

bool aes128_ctr_encrypt(const uint8_t* data, size_t data_len, const uint8_t* key, const uint8_t* iv, uint8_t* output) {
    return aes128_ctr_process(data, data_len, key, iv, output);
}

bool aes128_ctr_decrypt(const uint8_t* data, size_t data_len, const uint8_t* key, const uint8_t* iv, uint8_t* output) {
    return aes128_ctr_process(data, data_len, key, iv, output);
}
