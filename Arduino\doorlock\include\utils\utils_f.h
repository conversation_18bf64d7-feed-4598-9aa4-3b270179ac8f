#ifndef HW_S_UTILS_F_H
#define HW_S_UTILS_F_H

#include "../common.h"
#include "../super_password.h"
#include "aes_ctr.h"
#include "utils.h"
#include "json.h"
#include "../databases/initialize.h"
#include "../databases/configuration_database.h"
#include "../databases/counter.h"

// 获取设备名称（用户设置），custom_device_name 的长度建议至少为 50 位
bool get_custom_device_name(char *custom_device_name);

// 获取设备使用模式
int get_device_mode();

// 设置设备使用模式
bool set_device_mode(int device_mode);

// 获取超级管理员密码
bool get_super_password(String &password);

// 获取管理员密码
bool get_admin_password(String &password);

// 修改管理员密码
bool modify_admin_password(String &password);

#endif
