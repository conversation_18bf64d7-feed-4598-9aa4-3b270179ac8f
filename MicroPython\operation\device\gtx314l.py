from core.keypad.gtx314l import GTX314L


class GTX314LOperation():
    def __init__(self, scl, sda, addr):
        layout = ["1", "4", "*", "7", "#", "0", "8", "9", "6", "3", "5", "2"]
        self.__gtx314l = GTX314L(scl=scl, sda=sda, addr=addr, delay=10, sensitivity=0x05, keypad_layout=layout)

    def add_listener(self, numbers, callback):
        return self.__gtx314l.add_listener(numbers=numbers, callback=callback)

    def create_password(self, _id, numbers):
        return self.__gtx314l.create_password(_id=_id, numbers=numbers)

    def remove_password(self, _id):
        return self.__gtx314l.remove_password(_id=_id)

    def record_key(self):
        return self.__gtx314l.record_key(max_key=1, enable_only_not_press=True)

    def record_key_continuous(self):
        return self.__gtx314l.record_key(max_key=1, enable_only_not_press=False)
