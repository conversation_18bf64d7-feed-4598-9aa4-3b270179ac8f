import binascii


def to_hex_string(number, length):
    hex_number = hex(number)
    hex_number = hex_number.replace("0x", "")
    fill_length = length - len(hex_number)
    if fill_length > 0:
        hex_number = fill_length * "0" + hex_number
    hex_number = hex_number.upper()
    return hex_number


def string_to_hex(string, encode_type):
    string = string.encode(encode_type)
    string = binascii.hexlify(string)
    string = string.decode()
    return string
