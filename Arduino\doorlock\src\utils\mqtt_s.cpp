#include "../../include/utils/mqtt_s.h"

// 最近一次 MQTT 报告时间
unsigned long last_mqtt_report_time = 0;
// MQTT 连接状态  0 = 未连接  1 = 已准备连接  2 = 已连接
unsigned short mqtt_connect_status = 0;

// WiFiClient
WiFiClient esp32_wifi_client;
// PubSubClient
PubSubClient esp32_mqtt_client(esp32_wifi_client);

// 检查 MQTT 是否连接
bool mqtt_is_connected()
{
  return esp32_mqtt_client.connected();
}

// 连接 MQTT 服务器
void try_connect_mqtt(void (*mqtt_callback)(char *, uint8_t *, unsigned int))
{
  if (mqtt_connect_status == 0 && wifi_is_connected())
  {
    String mqtt_username;
    String mqtt_password;
    String mqtt_server;
    bool has_mqtt_username = ConfigurationDatabase::getInstance().getConfig("mqtt_username", mqtt_username);
    bool has_mqtt_password = ConfigurationDatabase::getInstance().getConfig("mqtt_password", mqtt_password);
    bool has_mqtt_server = ConfigurationDatabase::getInstance().getConfig("mqtt_server", mqtt_server);
    if (has_mqtt_username && has_mqtt_password && has_mqtt_server)
    {
      esp32_mqtt_client.setServer(mqtt_server.c_str(), mqtt_port);
      esp32_mqtt_client.setKeepAlive(3600);
      esp32_mqtt_client.setBufferSize(4096);
      esp32_mqtt_client.setCallback(mqtt_callback);
      esp32_mqtt_client.connect("esp32", mqtt_username.c_str(), mqtt_password.c_str());
    }
    else
    {
      print_log("mqtt", "init", "no mqtt config info!!");
    }
    mqtt_connect_status = 1;
  }
  if (mqtt_connect_status == 1 && mqtt_is_connected())
  {
    char mqtt_topic_self[50];
    get_mqtt_topic_self(mqtt_topic_self);
    esp32_mqtt_client.subscribe(MQTT_TOPIC_PUBLIC);
    esp32_mqtt_client.subscribe(mqtt_topic_self);
    mqtt_connect_status = 2;
  }
}

// 发布 MQTT 信息
bool mqtt_publish(const char *topic, const char *message)
{
  if (mqtt_is_connected())
  {
    esp32_mqtt_client.publish(topic, message);
    return true;
  }
  else
  {
    return false;
  }
}

// 接收 MQTT 信息
bool mqtt_receive()
{
  if (mqtt_is_connected())
  {
    esp32_mqtt_client.loop();
    return true;
  }
  else
  {
    return false;
  }
}

// MQTT 计数器报告
void mqtt_report_counter()
{
  char mqtt_topic_self[50];
  get_mqtt_topic_self(mqtt_topic_self);
  String counter_string;
  Counter::getInstance().getAllCount(counter_string);
  mqtt_publish(mqtt_topic_self, counter_string.c_str());
}

// MQTT 更新报告
void mqtt_report_update(int fx_id, bool state)
{
  DynamicJsonDocument doc(512);
  JsonObject lock_received = doc.createNestedObject("lock_received");
  lock_received["fx_id"] = fx_id;
  if (state)
  {
    lock_received["state"] = "ok";
  }
  else
  {
    lock_received["state"] = "err";
  }
  String report_string;
  serializeJson(doc, report_string);
  mqtt_publish(MQTT_TOPIC_PUBLIC, report_string.c_str());
}

// MQTT 状态信息报告 & 请求下发数据
void mqtt_report_states()
{
  if (last_mqtt_report_time == 0 || get_time_diff(last_mqtt_report_time) > MQTT_REPORT_INTERVAL)
  {
    char entity_id[50];
    char device_name[50];
    char custom_device_name[50];
    char mqtt_topic_self[50];
    get_entity_id(entity_id);
    get_device_name(device_name);
    bool has_custom_device_name = get_custom_device_name(custom_device_name);
    get_mqtt_topic_self(mqtt_topic_self);

    float battery_percent = get_battery_percent(PIN_BATTERY, BATTERY_FULL_VALUE, BATTERY_EMPTY_VALUE);

    DynamicJsonDocument doc_status(1024);
    JsonObject info_status = doc_status.createNestedObject("lock_info");
    info_status["entity_id"] = entity_id;
    info_status["device_name"] = device_name;
    info_status["device_status"] = "on";
    info_status["electric_quantity"] = battery_percent;
    info_status["lock_topic"] = mqtt_topic_self;
    if (has_custom_device_name)
    {
      info_status["as_device_name"] = custom_device_name;
    }
    else
    {
      info_status["as_device_name"] = "";
    }
    info_status["ip_address"] = WiFi.localIP().toString();
    String status_string;
    serializeJson(doc_status, status_string);

    DynamicJsonDocument doc_action(512);
    JsonObject info_action = doc_action.createNestedObject("lock_action");
    info_action["lock_topic"] = mqtt_topic_self;
    info_action["msg"] = "ok";
    String action_string;
    serializeJson(doc_action, action_string);

    bool status_is_published = mqtt_publish(MQTT_TOPIC_PUBLIC, status_string.c_str());
    bool action_is_published = mqtt_publish(MQTT_TOPIC_PUBLIC, action_string.c_str());
    if (status_is_published && action_is_published)
    {
      last_mqtt_report_time = millis();
    }
  }
}

// MQTT 操作记录报告
void mqtt_report_operation()
{
  if (mqtt_is_connected())
  {
    for (int t = 0; t < 100; t++)
    {
      OperationRecord record;
      OperationDatabase::getInstance().get(0, record);
      int uid = record.uid;
      int type = record.type;
      time_t timestamp = record.timestamp;
      char mqtt_topic_self[50];
      get_mqtt_topic_self(mqtt_topic_self);
      if (OperationDatabase::getInstance().remove(0))
      {
        DynamicJsonDocument doc(512);
        JsonObject info_log = doc.createNestedObject("lock_log");
        info_log["f_id"] = uid;
        info_log["lock_topic"] = mqtt_topic_self;
        info_log["lock_action"] = "on";
        info_log["time"] = timestamp;
        if (type == OPERATION_TYPE_KEYPAD)
        {
          info_log["type"] = "pwd";
        }
        else if (type == OPERATION_TYPE_FPM383)
        {
          info_log["type"] = "fin";
        }
        String log_string;
        serializeJson(doc, log_string);
        mqtt_publish(MQTT_TOPIC_PUBLIC, log_string.c_str());
      }
      else
      {
        OperationDatabase::getInstance().store();
        break;
      }
    }
  }
}
