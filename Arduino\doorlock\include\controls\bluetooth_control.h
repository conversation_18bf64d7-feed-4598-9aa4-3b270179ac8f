#ifndef HW_S_BLUETOOTH_CONTROL_H
#define HW_S_BLUETOOTH_CONTROL_H

#include "../common.h"
#include "../utils/utils.h"
#include "../utils/bluetooth_s.h"

// 获取蓝牙连接状态
bool bluetooth_is_connected();

// 获取蓝牙连接 ID
uint16_t get_bluetooth_connect_id();

// 获取蓝牙等待状态
bool get_bluetooth_waiting_status();

// 获取合并数据包
void get_bluetooth_complete_data(std::string &data);

// 完成蓝牙数据处理
void finish_bluetooth_data_process();

// 蓝牙服务回调函数
class BluetoothServerCallbacks : public BLEServerCallbacks
{
public:
  void onConnect(BLEServer *pServer, esp_ble_gatts_cb_param_t *param);
  void onDisconnect(BLEServer *pServer, esp_ble_gatts_cb_param_t *param);
};

// 蓝牙信息回调函数
class BluetoothCharacteristicCallbacks : public BLECharacteristicCallbacks
{
public:
  void onWrite(BLECharacteristic *pCharacteristic);
};

#endif
