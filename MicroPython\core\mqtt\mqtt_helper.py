from core.config.config import Config<PERSON>elper
from core.mqtt.mqtt_api import MQTT<PERSON>lient
from core.mqtt.mqtt_message import MQTTMessage
import sys
from utils.time_util import sleep


class MQTTHelper():
    __instance = None

    def __init__(self, client_id, callback=None):
        self.__conn = None
        self.__client_id = client_id
        self.__callback = callback
        self.__topic = []
        self.__config_name = self.__class__.__name__.lower()
        self.__config_helper = ConfigHelper.get_instance()
        self.__init_config()
        self.__server = self.__config_helper.get_configs(self.__config_name, "server")
        self.__user = self.__config_helper.get_configs(self.__config_name, "user")
        self.__password = self.__config_helper.get_configs(self.__config_name, "password")

    def __init_config(self):
        self.__config_helper.create_config([self.__config_name])

    @classmethod
    def get_instance(cls, *args, **kwargs):
        if not cls.__instance:
            cls.__instance = cls(*args, **kwargs)
        return cls.__instance

    def connect(self, topic=None):
        if topic and isinstance(topic, list):
            self.__topic = topic
        try:
            if not self.__conn:
                self.__conn = MQTTClient(
                    client_id=self.__client_id,
                    server=self.__server,
                    user=self.__user,
                    password=self.__password,
                    keepalive=3600
                )
                self.__conn.set_callback(self.__callback)
            self.__conn.connect()
            for topic in self.__topic:
                self.__conn.subscribe(topic.encode())
        except Exception as err:
            sys.print_exception(err)

    def disconnect(self):
        try:
            if self.__conn:
                self.__conn.disconnect()
        except Exception as err:
            sys.print_exception(err)

    def publish(self, topic, message, retain=False):
        try:
            self.__conn.publish(topic, message.encode(), retain=retain)
        except Exception as err:
            sys.print_exception(err)

    def delete_retain(self):
        self.publish("", True)

    def receive(self):
        try:
            self.__conn.wait_msg()
        except Exception as err:
            sys.print_exception(err)
            sleep(2000)
            self.disconnect()
            self.connect()
