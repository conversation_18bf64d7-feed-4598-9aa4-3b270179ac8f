#ifndef HW_S_MQTT_S_H
#define HW_S_MQTT_S_H

#include "../common.h"
#include "utils.h"
#include "utils_f.h"
#include "json.h"
#include "wifi_s.h"
#include "../databases/configuration_database.h"
#include "../databases/counter.h"
#include "../databases/operation_database.h"
#include <WiFiClient.h>
#include <PubSubClient.h>

// 检查 MQTT 是否连接
bool mqtt_is_connected();

// 连接 MQTT 服务器
void try_connect_mqtt(void (*mqtt_callback)(char *, uint8_t *, unsigned int));

// 发布 MQTT 信息
bool mqtt_publish(const char *topic, const char *message);

// 接收 MQTT 信息
bool mqtt_receive();

// MQTT 计数器报告
void mqtt_report_counter();

// MQTT 更新报告
void mqtt_report_update(int fx_id, bool state);

// MQTT 状态信息报告 & 请求下发数据
void mqtt_report_states();

// MQTT 操作记录报告
void mqtt_report_operation();

#endif
