from core.fingerprint.fpm383 import FPM383


class FPM383Operation():
    def __init__(self, rx, tx, index):
        self.__fpm383 = FPM383(rx=rx, tx=tx, index=index)

    def create_reference_id(self, ref_id, _id):
        return self.__fpm383.create_reference_id(ref_id=ref_id, _id=_id)

    def remove_reference_id(self, ref_id):
        return self.__fpm383.remove_reference_id(ref_id=ref_id)

    def is_press(self, callback=None):
        return self.__fpm383.is_press(callback=callback)

    def open_led_base(self):
        return self.__fpm383.set_led(
            mode=FPM383.LED_MODE_PWM,
            color=FPM383.LED_BLUE,
            p1=100,
            p2=0,
            p3=60
        )

    def open_led_success(self):
        return self.__fpm383.set_led(
            mode=FPM383.LED_MODE_FLASH,
            color=FPM383.LED_GREEN,
            p1=20,
            p2=20,
            p3=5
        )

    def open_led_fail(self):
        return self.__fpm383.set_led(
            mode=FPM383.LED_MODE_FLASH,
            color=FPM383.LED_RED,
            p1=20,
            p2=20,
            p3=5
        )

    def close_led(self):
        return self.__fpm383.set_led(
            mode=FPM383.LED_MODE_CLOSE,
            color=FPM383.LED_NONE
        )

    def sleep(self):
        return self.__fpm383.sleep()

    def register(self, callback=None):
        return self.__fpm383.auto_register(callback=callback)

    def validate(self):
        return self.__fpm383.auto_validate()

    def cancel(self):
        return self.__fpm383.cancel()

    def delete_model(self, _id):
        return self.__fpm383.delete_model(from_id=_id, count=1)

    def upload(self, _id):
        status = self.__fpm383.load_model(_id)
        if status:
            return self.__fpm383.upload_model()

    def download(self, data):
        self.__fpm383.download_model(data)
        return self.__fpm383.save_model()
