#include "../../include/utils/utils.h"

// 检查一个字符串是否是数字字符串
bool is_numeric_string(String &str)
{
  int converted_number = str.toInt();
  String converted_string = String(converted_number);
  return str == converted_string;
}

// 比较两个字符数组是否完全相同
bool compare_array(const char *c1, const char *c2, int size)
{
  for (int t = 0; t < size; t++)
  {
    if (c1[t] != c2[t])
    {
      return false;
    }
  }
  return true;
}

// 十六进制字符串转换字符数组
void hex_string_to_char_array(String &hex_string, char *char_array, int length)
{
  for (int t = 0; t < length; t++)
  {
    char_array[t] = strtol(hex_string.substring(t * 2, t * 2 + 2).c_str(), nullptr, 16);
  }
}

// 数值转十六进制字符串，可指定长度
void number_to_hex_string(int number, int length, String &hex_string)
{
  String hex = String(number, HEX);
  hex.toUpperCase();
  while (hex.length() < length)
  {
    hex = "0" + hex;
  }
  hex_string = hex;
}

// 打印十六进制字符
void print_hex(char hex_char)
{
#ifdef DEBUG_MODE
  Serial.println(hex_char, HEX);
#endif
}

// 行内打印十六进制字符数组
void print_hex_inline(const char *data, size_t length)
{
#ifdef DEBUG_MODE
  for (size_t t = 0; t < length; t++)
  {
    if (data[t] < 0x10)
    {
      Serial.print("0");
    }
    Serial.print(data[t], HEX);
    Serial.print(" ");
  }
  Serial.println();
#endif
}

// 打印日志信息
void print_log(const String &title, const String &desc, const String &message)
{
#ifdef DEBUG_MODE
  size_t size = title.length() + desc.length() + message.length() + 15;
  char log_content[size];
  sprintf(log_content, "[[%s:::%s]] %s", title.c_str(), desc.c_str(), message.c_str());
  Serial.println(log_content);
#endif
}

// 获取随机数
uint32_t get_random_number()
{
  return esp_random();
}

// 生成随机 uint8_t 数组
void generate_random_uint8(uint8_t *data, size_t length)
{
  for (size_t t = 0; t < length; t++)
  {
    data[t] = esp_random() & 0xFF;
  }
}

// 生成随机字符串
void generate_random_string(size_t length, bool include_digits, bool include_lowercase, bool include_uppercase, String &str)
{
  String charset = "";
  str = "";
  if (include_digits)
  {
    charset += "0123456789";
  }
  if (include_lowercase)
  {
    charset += "abcdefghijklmnopqrstuvwxyz";
  }
  if (include_uppercase)
  {
    charset += "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  }
  if (charset.length() > 0)
  {
    for (size_t t = 0; t < length; t++)
    {
      size_t index = esp_random() % charset.length();
      str += charset[index];
    }
  }
}

// 获取时间差
unsigned long get_time_diff(unsigned long time)
{
  return millis() - time;
}

// 获取设备电量百分比
float get_battery_percent(uint8_t pin, int full_charged_value, int empty_value)
{
  int battery_value = analogRead(pin);
  float battery_percent = (float)(battery_value - empty_value) / (full_charged_value - empty_value) * 100.0;
  return battery_percent;
}

// LED 闪烁
void led_blink(uint8_t pin, int on_time, int off_time, int num_flashes)
{
  for (int t = 0; t < num_flashes; t++)
  {
    digitalWrite(pin, HIGH);
    delay(on_time);
    digitalWrite(pin, LOW);
    delay(off_time);
  }
}

// LED 闪烁（使用计数器）
void led_blink_using_counter(uint8_t pin, unsigned long on_count, unsigned long off_count, unsigned long counter)
{
  unsigned long cycle = on_count + off_count;
  unsigned long phase = counter % cycle;
  if (phase < on_count)
  {
    digitalWrite(pin, HIGH);
  }
  else
  {
    digitalWrite(pin, LOW);
  }
}

// 获取唯一标识，unique_id 的长度建议至少为 18 位
void get_unique_id(char *unique_id)
{
  uint64_t uid = ESP.getEfuseMac();
  sprintf(unique_id, "%04x%08x", (uint16_t)(uid >> 32), (uint32_t)uid);
}

// 获取设备标识，entity_id 的长度建议至少为 50 位
void get_entity_id(char *entity_id)
{
  char unique_id[18];
  get_unique_id(unique_id);
  sprintf(entity_id, "doorlock-entity-%s", unique_id);
}

// 获取设备名称，device_name 的长度建议至少为 50 位
void get_device_name(char *device_name)
{
  char unique_id[18];
  get_unique_id(unique_id);
  sprintf(device_name, "智能锁-id-%s", unique_id);
}

// 获取蓝牙名称，bluetooth_name 的长度建议至少为 50 位
void get_bluetooth_name(char *bluetooth_name)
{
  char unique_id[18];
  get_unique_id(unique_id);
  sprintf(bluetooth_name, "lock-%s", unique_id);
}

// 获取专有 MQTT 主题名称，mqtt_topic_self 的长度建议至少为 50 位
void get_mqtt_topic_self(char *mqtt_topic_self)
{
  char unique_id[18];
  get_unique_id(unique_id);
  sprintf(mqtt_topic_self, "lock/doorlock-mqtt-%s", unique_id);
}

// 生成指纹命令 v1
void get_fpm383_command_v1(char *command, const char *body, int body_length)
{
  const size_t header_size = 6;
  unsigned int checksum = 0;
  for (int t = 0; t < header_size; t++)
  {
    command[t] = FPM383_HEADER_V1[t];
  }
  for (int t = 0; t < body_length; t++)
  {
    command[t + header_size] = body[t];
    checksum += body[t];
  }
  checksum &= 0xFFFF;
  command[body_length + header_size] = (checksum >> 8) & 0xFF;
  command[body_length + header_size + 1] = checksum & 0xFF;
}

// 生成指纹命令 v2
void get_fpm383_command_v2(char *command, const char *body, int body_length)
{
  const size_t header_size = 8;
  unsigned int checksum_header = 0;
  unsigned int checksum_body = 0;
  for (int t = 0; t < header_size; t++)
  {
    command[t] = FPM383_HEADER_V2[t];
  }
  command[header_size] = ((body_length + 1) >> 8) & 0xFF;
  command[header_size + 1] = (body_length + 1) & 0xFF;
  for (int t = 0; t < header_size + 2; t++)
  {
    checksum_header += command[t];
  }
  checksum_header = 0x100 - checksum_header % 0x100;
  command[header_size + 2] = checksum_header;
  for (int t = 0; t < body_length; t++)
  {
    command[t + header_size + 3] = body[t];
    checksum_body += body[t];
  }
  checksum_body = 0x100 - checksum_body % 0x100;
  command[body_length + header_size + 3] = checksum_body;
}

/**
 * 获取指纹 LED 命令
 *
 * LED 模式取值：关闭 0x00  开启 0x01  按下发光（不可用） 0x02  PWM渐变 0x03  FLASH闪烁 0x04
 * LED 颜色取值：绿色 0x01  红色 0x02  蓝色 0x04
 *
 * color 参数为 LED 颜色，如需同时点亮多个颜色，可以将多个颜色值按位或运算，例如 0x01 | 0x02
 * p1 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最大亮度（取值范围 0 - 100）和 LED 每次点亮时长（参数值 * 10ms）
 * p2 参数仅当模式为 PWM 或 FLASH 时起效，分别表示最小亮度（取值范围 0 - 100）和 LED 每次熄灭时长（参数值 * 10ms）
 * p3 参数仅当模式为 PWM 或 FLASH 时起效，分别表示亮度每秒变化速率（取值范围 0 - 100）和 LED 闪烁次数
 * 非 PWM 或 FLASH 模式下，p1 / p2 / p3 参数不起效，可以不传入相应参数，取默认值 0
 */
void get_fpm383_command_led(char *command, char mode, char color, char p1, char p2, char p3)
{
  const size_t body_length = 11;
  char body[body_length];
  const char body_fixed[] = {0x00, 0x00, 0x00, 0x00, 0x02, 0x0F};
  for (int t = 0; t < 6; t++)
  {
    body[t] = body_fixed[t];
  }
  body[6] = mode;
  body[7] = color;
  body[8] = p1;
  body[9] = p2;
  body[10] = p3;
  get_fpm383_command_v2(command, body, body_length);
}

// 获取指纹注册命令
void get_fpm383_command_register(char *command, int number)
{
  const size_t body_length = 9;
  char body[body_length];
  const char body_fixed[] = {0x01, 0x00, 0x08, 0x31};
  for (int t = 0; t < 4; t++)
  {
    body[t] = body_fixed[t];
  }
  body[4] = (number >> 8) & 0xFF;
  body[5] = number & 0xFF;
  body[6] = 0x04;
  body[7] = 0x00;
  body[8] = 0x0B;
  get_fpm383_command_v1(command, body, body_length);
}

// 获取指纹储存模板命令
void get_fpm383_command_store(char *command, int number)
{
  const size_t body_length = 7;
  char body[body_length];
  const char body_fixed[] = {0x01, 0x00, 0x06, 0x06, 0x01};
  for (int t = 0; t < 5; t++)
  {
    body[t] = body_fixed[t];
  }
  body[5] = (number >> 8) & 0xFF;
  body[6] = number & 0xFF;
  get_fpm383_command_v1(command, body, body_length);
}

// 获取指纹删除命令
void get_fpm383_command_remove(char *command, int number)
{
  const size_t body_length = 8;
  char body[body_length];
  const char body_fixed[] = {0x01, 0x00, 0x07, 0x0C};
  for (int t = 0; t < 4; t++)
  {
    body[t] = body_fixed[t];
  }
  body[4] = (number >> 8) & 0xFF;
  body[5] = number & 0xFF;
  body[6] = 0x00;
  body[7] = 0x01;
  get_fpm383_command_v1(command, body, body_length);
}
