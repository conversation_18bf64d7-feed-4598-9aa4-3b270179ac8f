#include "../../include/utils/wifi_s.h"

// 检查 WiFi 是否连接
bool wifi_is_connected()
{
  return WiFi.status() == WL_CONNECTED;
}

// 初始化 WiFi
void wifi_init()
{
  String wifi_id;
  String wifi_password;
  bool has_wifi_id = ConfigurationDatabase::getInstance().getConfig("wifi_id", wifi_id);
  bool has_wifi_password = ConfigurationDatabase::getInstance().getConfig("wifi_password", wifi_password);
  if (has_wifi_id && has_wifi_password)
  {
    WiFi.setTxPower(WIFI_POWER_2dBm);
    WiFi.begin(wifi_id.c_str(), wifi_password.c_str());
  }
  else
  {
    print_log("wifi", "init", "no wifi config info!!");
  }
}
