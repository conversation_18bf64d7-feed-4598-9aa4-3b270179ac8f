#include "../../include/controls/sleep_control.h"

// 当前睡眠超时时间
unsigned long current_sleep_timeout = SLEEP_TIMEOUT_NORMAL;
// 最近一次睡眠计时器报告时间
unsigned long last_sleep_report_time = 0;

// 获取最近一次睡眠计时器报告时间
unsigned long get_last_sleep_report_time()
{
  return last_sleep_report_time;
}

// 设置睡眠超时时间
void set_sleep_timeout(unsigned long timeout)
{
  current_sleep_timeout = timeout;
}

// 睡眠计时器报告
void sleep_report()
{
  last_sleep_report_time = millis();
}

// 睡眠判断
void try_sleep(void (*before_sleep)())
{
  if (get_time_diff(get_last_sleep_report_time()) > current_sleep_timeout)
  {
    print_log("sleep", "try", "ready to start deep sleep!!");
    before_sleep();
    delay(100);
    esp_deep_sleep_start();
  }
}
